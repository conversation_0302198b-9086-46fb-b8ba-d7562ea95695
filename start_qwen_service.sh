#!/bin/bash
# Qwen-Agent 服务启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== 启动 Qwen-Agent 服务 ===${NC}"

# 检查 Python 版本
echo -e "${YELLOW}检查 Python 环境...${NC}"
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}错误: 未找到 python3${NC}"
    exit 1
fi

PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "Python 版本: $PYTHON_VERSION"

# 检查是否需要创建虚拟环境
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}创建虚拟环境...${NC}"
    python3 -m venv venv
fi

# 激活虚拟环境
echo -e "${YELLOW}激活虚拟环境...${NC}"
source venv/bin/activate

# 安装依赖
echo -e "${YELLOW}安装依赖包...${NC}"
pip install -r requirements.txt

# 检查 Ollama 服务
echo -e "${YELLOW}检查 Ollama 服务...${NC}"
if ! curl -s http://localhost:11434/api/tags > /dev/null; then
    echo -e "${RED}警告: Ollama 服务未运行或不可访问${NC}"
    echo "请确保 Ollama 服务正在运行: ollama serve"
    echo "并且已安装 qwen3:32b 模型: ollama pull qwen3:32b"
fi

# 检查 MCP 文件服务器
if [ ! -f "mcp_file_server.py" ]; then
    echo -e "${YELLOW}警告: 未找到 mcp_file_server.py${NC}"
    echo "MCP 文件操作功能可能不可用"
fi

# 启动服务
echo -e "${GREEN}启动 Qwen-Agent 服务...${NC}"
echo "服务地址: http://127.0.0.1:5000"
echo "健康检查: http://127.0.0.1:5000/health"
echo "按 Ctrl+C 停止服务"
echo ""

export PYTHONIOENCODING=UTF-8
python3 qwen_agent_service.py
