MiniTool 文档上传功能测试

这是一个专门用于测试MiniTool文档上传功能的测试文档。

## 功能特点

1. **智能聊天界面**
   - 支持与AI模型进行对话
   - 实时流式输出显示
   - 自动滚动到最新消息

2. **文档上传功能**
   - 点击"📄 上传文档"按钮
   - 支持多种文件格式：PDF、Word、TXT、HTML、Markdown
   - 自动处理文档并添加到知识库
   - 实时显示上传状态和进度

3. **RAG技术集成**
   - 基于ChromaDB向量数据库
   - 使用all-MiniLM-L6-v2嵌入模型
   - 支持语义搜索和相似性匹配
   - 文档内容自动分块处理

## 使用方法

1. 启动MiniTool应用程序
2. 进入智能聊天界面
3. 点击绿色的"📄 上传文档"按钮
4. 在文件选择对话框中选择要上传的文档
5. 等待上传完成，系统会显示成功消息
6. 现在可以询问关于文档内容的问题

## 技术实现

- **前端界面**: wxWidgets C++框架
- **后端服务**: Qwen-Agent Python服务
- **AI模型**: Qwen3:8b本地模型
- **向量数据库**: ChromaDB
- **文档处理**: 支持多格式解析和分块
- **网络通信**: HTTP API调用

## 测试场景

上传此文档后，您可以测试以下问题：
- "这个文档介绍了什么功能？"
- "如何使用文档上传功能？"
- "MiniTool支持哪些文件格式？"
- "RAG技术是如何实现的？"

系统应该能够基于此文档内容提供准确的回答。

## 预期结果

- 文档上传成功后会显示绿色的成功消息
- 聊天区域会显示"📄 文档上传成功: upload_test.txt"
- 可以正常询问文档相关问题并获得准确回答
- 上传过程中按钮会被禁用防止重复操作

这个测试文档包含了完整的功能说明和使用指南，可以很好地验证RAG功能是否正常工作。
