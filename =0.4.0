Requirement already satisfied: chromadb in ./venv/lib/python3.11/site-packages (1.0.15)
Requirement already satisfied: build>=1.0.3 in ./venv/lib/python3.11/site-packages (from chromadb) (1.3.0)
Requirement already satisfied: pydantic>=1.9 in ./venv/lib/python3.11/site-packages (from chromadb) (2.9.2)
Requirement already satisfied: pybase64>=1.4.1 in ./venv/lib/python3.11/site-packages (from chromadb) (1.4.2)
Requirement already satisfied: uvicorn>=0.18.3 in ./venv/lib/python3.11/site-packages (from uvicorn[standard]>=0.18.3->chromadb) (0.35.0)
Requirement already satisfied: numpy>=1.22.5 in ./venv/lib/python3.11/site-packages (from chromadb) (2.3.1)
Requirement already satisfied: posthog<6.0.0,>=2.4.0 in ./venv/lib/python3.11/site-packages (from chromadb) (5.4.0)
Requirement already satisfied: typing-extensions>=4.5.0 in ./venv/lib/python3.11/site-packages (from chromadb) (4.14.0)
Requirement already satisfied: onnxruntime>=1.14.1 in ./venv/lib/python3.11/site-packages (from chromadb) (1.22.1)
Requirement already satisfied: opentelemetry-api>=1.2.0 in ./venv/lib/python3.11/site-packages (from chromadb) (1.36.0)
Requirement already satisfied: opentelemetry-exporter-otlp-proto-grpc>=1.2.0 in ./venv/lib/python3.11/site-packages (from chromadb) (1.36.0)
Requirement already satisfied: opentelemetry-sdk>=1.2.0 in ./venv/lib/python3.11/site-packages (from chromadb) (1.36.0)
Requirement already satisfied: tokenizers>=0.13.2 in ./venv/lib/python3.11/site-packages (from chromadb) (0.21.4)
Requirement already satisfied: pypika>=0.48.9 in ./venv/lib/python3.11/site-packages (from chromadb) (0.48.9)
Requirement already satisfied: tqdm>=4.65.0 in ./venv/lib/python3.11/site-packages (from chromadb) (4.67.1)
Requirement already satisfied: overrides>=7.3.1 in ./venv/lib/python3.11/site-packages (from chromadb) (7.7.0)
Requirement already satisfied: importlib-resources in ./venv/lib/python3.11/site-packages (from chromadb) (6.5.2)
Requirement already satisfied: grpcio>=1.58.0 in ./venv/lib/python3.11/site-packages (from chromadb) (1.74.0)
Requirement already satisfied: bcrypt>=4.0.1 in ./venv/lib/python3.11/site-packages (from chromadb) (4.3.0)
Requirement already satisfied: typer>=0.9.0 in ./venv/lib/python3.11/site-packages (from chromadb) (0.16.0)
Requirement already satisfied: kubernetes>=28.1.0 in ./venv/lib/python3.11/site-packages (from chromadb) (33.1.0)
Requirement already satisfied: tenacity>=8.2.3 in ./venv/lib/python3.11/site-packages (from chromadb) (9.1.2)
Requirement already satisfied: pyyaml>=6.0.0 in ./venv/lib/python3.11/site-packages (from chromadb) (6.0.2)
Requirement already satisfied: mmh3>=4.0.1 in ./venv/lib/python3.11/site-packages (from chromadb) (5.2.0)
Requirement already satisfied: orjson>=3.9.12 in ./venv/lib/python3.11/site-packages (from chromadb) (3.10.18)
Requirement already satisfied: httpx>=0.27.0 in ./venv/lib/python3.11/site-packages (from chromadb) (0.28.1)
Requirement already satisfied: rich>=10.11.0 in ./venv/lib/python3.11/site-packages (from chromadb) (14.0.0)
Requirement already satisfied: jsonschema>=4.19.0 in ./venv/lib/python3.11/site-packages (from chromadb) (4.24.0)
Requirement already satisfied: requests<3.0,>=2.7 in ./venv/lib/python3.11/site-packages (from posthog<6.0.0,>=2.4.0->chromadb) (2.32.4)
Requirement already satisfied: six>=1.5 in ./venv/lib/python3.11/site-packages (from posthog<6.0.0,>=2.4.0->chromadb) (1.17.0)
Requirement already satisfied: python-dateutil>=2.2 in ./venv/lib/python3.11/site-packages (from posthog<6.0.0,>=2.4.0->chromadb) (2.9.0.post0)
Requirement already satisfied: backoff>=1.10.0 in ./venv/lib/python3.11/site-packages (from posthog<6.0.0,>=2.4.0->chromadb) (2.2.1)
Requirement already satisfied: distro>=1.5.0 in ./venv/lib/python3.11/site-packages (from posthog<6.0.0,>=2.4.0->chromadb) (1.9.0)
Requirement already satisfied: charset_normalizer<4,>=2 in ./venv/lib/python3.11/site-packages (from requests<3.0,>=2.7->posthog<6.0.0,>=2.4.0->chromadb) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in ./venv/lib/python3.11/site-packages (from requests<3.0,>=2.7->posthog<6.0.0,>=2.4.0->chromadb) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in ./venv/lib/python3.11/site-packages (from requests<3.0,>=2.7->posthog<6.0.0,>=2.4.0->chromadb) (2.5.0)
Requirement already satisfied: certifi>=2017.4.17 in ./venv/lib/python3.11/site-packages (from requests<3.0,>=2.7->posthog<6.0.0,>=2.4.0->chromadb) (2025.6.15)
Requirement already satisfied: packaging>=19.1 in ./venv/lib/python3.11/site-packages (from build>=1.0.3->chromadb) (25.0)
Requirement already satisfied: pyproject_hooks in ./venv/lib/python3.11/site-packages (from build>=1.0.3->chromadb) (1.2.0)
Requirement already satisfied: anyio in ./venv/lib/python3.11/site-packages (from httpx>=0.27.0->chromadb) (4.9.0)
Requirement already satisfied: httpcore==1.* in ./venv/lib/python3.11/site-packages (from httpx>=0.27.0->chromadb) (1.0.9)
Requirement already satisfied: h11>=0.16 in ./venv/lib/python3.11/site-packages (from httpcore==1.*->httpx>=0.27.0->chromadb) (0.16.0)
Requirement already satisfied: attrs>=22.2.0 in ./venv/lib/python3.11/site-packages (from jsonschema>=4.19.0->chromadb) (25.3.0)
Requirement already satisfied: jsonschema-specifications>=2023.03.6 in ./venv/lib/python3.11/site-packages (from jsonschema>=4.19.0->chromadb) (2025.4.1)
Requirement already satisfied: referencing>=0.28.4 in ./venv/lib/python3.11/site-packages (from jsonschema>=4.19.0->chromadb) (0.36.2)
Requirement already satisfied: rpds-py>=0.7.1 in ./venv/lib/python3.11/site-packages (from jsonschema>=4.19.0->chromadb) (0.26.0)
Requirement already satisfied: google-auth>=1.0.1 in ./venv/lib/python3.11/site-packages (from kubernetes>=28.1.0->chromadb) (2.40.3)
Requirement already satisfied: websocket-client!=0.40.0,!=0.41.*,!=0.42.*,>=0.32.0 in ./venv/lib/python3.11/site-packages (from kubernetes>=28.1.0->chromadb) (1.8.0)
Requirement already satisfied: requests-oauthlib in ./venv/lib/python3.11/site-packages (from kubernetes>=28.1.0->chromadb) (2.0.0)
Requirement already satisfied: oauthlib>=3.2.2 in ./venv/lib/python3.11/site-packages (from kubernetes>=28.1.0->chromadb) (3.3.1)
Requirement already satisfied: durationpy>=0.7 in ./venv/lib/python3.11/site-packages (from kubernetes>=28.1.0->chromadb) (0.10)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in ./venv/lib/python3.11/site-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in ./venv/lib/python3.11/site-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in ./venv/lib/python3.11/site-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb) (4.9.1)
Requirement already satisfied: pyasn1>=0.1.3 in ./venv/lib/python3.11/site-packages (from rsa<5,>=3.1.4->google-auth>=1.0.1->kubernetes>=28.1.0->chromadb) (0.6.1)
Requirement already satisfied: coloredlogs in ./venv/lib/python3.11/site-packages (from onnxruntime>=1.14.1->chromadb) (15.0.1)
Requirement already satisfied: flatbuffers in ./venv/lib/python3.11/site-packages (from onnxruntime>=1.14.1->chromadb) (25.2.10)
Requirement already satisfied: protobuf in ./venv/lib/python3.11/site-packages (from onnxruntime>=1.14.1->chromadb) (6.31.1)
Requirement already satisfied: sympy in ./venv/lib/python3.11/site-packages (from onnxruntime>=1.14.1->chromadb) (1.14.0)
Requirement already satisfied: importlib-metadata<8.8.0,>=6.0 in ./venv/lib/python3.11/site-packages (from opentelemetry-api>=1.2.0->chromadb) (8.7.0)
Requirement already satisfied: zipp>=3.20 in ./venv/lib/python3.11/site-packages (from importlib-metadata<8.8.0,>=6.0->opentelemetry-api>=1.2.0->chromadb) (3.23.0)
Requirement already satisfied: googleapis-common-protos~=1.57 in ./venv/lib/python3.11/site-packages (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb) (1.70.0)
Requirement already satisfied: opentelemetry-exporter-otlp-proto-common==1.36.0 in ./venv/lib/python3.11/site-packages (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb) (1.36.0)
Requirement already satisfied: opentelemetry-proto==1.36.0 in ./venv/lib/python3.11/site-packages (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb) (1.36.0)
Requirement already satisfied: opentelemetry-semantic-conventions==0.57b0 in ./venv/lib/python3.11/site-packages (from opentelemetry-sdk>=1.2.0->chromadb) (0.57b0)
Requirement already satisfied: annotated-types>=0.6.0 in ./venv/lib/python3.11/site-packages (from pydantic>=1.9->chromadb) (0.7.0)
Requirement already satisfied: pydantic-core==2.23.4 in ./venv/lib/python3.11/site-packages (from pydantic>=1.9->chromadb) (2.23.4)
Requirement already satisfied: markdown-it-py>=2.2.0 in ./venv/lib/python3.11/site-packages (from rich>=10.11.0->chromadb) (3.0.0)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in ./venv/lib/python3.11/site-packages (from rich>=10.11.0->chromadb) (2.19.2)
Requirement already satisfied: mdurl~=0.1 in ./venv/lib/python3.11/site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->chromadb) (0.1.2)
Requirement already satisfied: huggingface-hub<1.0,>=0.16.4 in ./venv/lib/python3.11/site-packages (from tokenizers>=0.13.2->chromadb) (0.34.3)
Requirement already satisfied: filelock in ./venv/lib/python3.11/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb) (3.18.0)
Requirement already satisfied: fsspec>=2023.5.0 in ./venv/lib/python3.11/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb) (2025.5.1)
Requirement already satisfied: hf-xet<2.0.0,>=1.1.3 in ./venv/lib/python3.11/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb) (1.1.5)
Requirement already satisfied: click>=8.0.0 in ./venv/lib/python3.11/site-packages (from typer>=0.9.0->chromadb) (8.2.1)
Requirement already satisfied: shellingham>=1.3.0 in ./venv/lib/python3.11/site-packages (from typer>=0.9.0->chromadb) (1.5.4)
Requirement already satisfied: httptools>=0.6.3 in ./venv/lib/python3.11/site-packages (from uvicorn[standard]>=0.18.3->chromadb) (0.6.4)
Requirement already satisfied: python-dotenv>=0.13 in ./venv/lib/python3.11/site-packages (from uvicorn[standard]>=0.18.3->chromadb) (1.1.1)
Requirement already satisfied: uvloop>=0.15.1 in ./venv/lib/python3.11/site-packages (from uvicorn[standard]>=0.18.3->chromadb) (0.21.0)
Requirement already satisfied: watchfiles>=0.13 in ./venv/lib/python3.11/site-packages (from uvicorn[standard]>=0.18.3->chromadb) (1.1.0)
Requirement already satisfied: websockets>=10.4 in ./venv/lib/python3.11/site-packages (from uvicorn[standard]>=0.18.3->chromadb) (15.0.1)
Requirement already satisfied: sniffio>=1.1 in ./venv/lib/python3.11/site-packages (from anyio->httpx>=0.27.0->chromadb) (1.3.1)
Requirement already satisfied: humanfriendly>=9.1 in ./venv/lib/python3.11/site-packages (from coloredlogs->onnxruntime>=1.14.1->chromadb) (10.0)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in ./venv/lib/python3.11/site-packages (from sympy->onnxruntime>=1.14.1->chromadb) (1.3.0)
