#ifndef MAINFRAME_H
#define MAINFRAME_H

#include <wx/wx.h>
#include <vector>
#include "LoginDialog.h"

class MainFrame : public wxFrame
{
public:
    MainFrame(const wxString& title, const wxPoint& pos, const wxSize& size);

    // Login management
    bool ShowLoginDialog();
    void SetLoggedIn(bool loggedIn);
    bool IsLoggedIn() const { return m_isLoggedIn; }

private:
    // Event handlers
    void OnDeleteSource(wxCommandEvent& event);
    void OnSystemInfo(wxCommandEvent& event);
    void OnUdpTest(wxCommandEvent& event);
    void OnChangeConfig(wxCommandEvent& event);
    void OnReplaceAlgorithm(wxCommandEvent& event);
    void OnAiChat(wxCommandEvent& event);
    void OnLogFilePack(wxCommandEvent& event);
    void OnButtonClick(wxCommandEvent& event);
    void OnExit(wxCommandEvent& event);
    void OnAbout(wxCommandEvent& event);

    // UI creation methods
    void CreateMenuBar();
    void CreateButtonGrid();
    void CreateStatusBar();

    // Button management
    void AddButton(const wxString& label, int id, bool enabled = true);
    void SetButtonFunction(int buttonIndex, const wxString& label, int id);

    // Member variables
    wxPanel* m_panel;
    wxGridSizer* m_buttonSizer;
    std::vector<wxButton*> m_buttons;

    // Login state
    bool m_isLoggedIn;
    wxString m_currentUser;
    
    // Constants for layout
    static const int BUTTONS_PER_ROW = 4;
    static const int INITIAL_ROWS = 4;
    static const int BUTTON_WIDTH = 150;
    static const int BUTTON_HEIGHT = 80;

    // Button IDs
    enum
    {
        ID_DELETE_SOURCE = 1001,
        ID_SYSTEM_INFO = 1002,
        ID_UDP_TEST = 1003,
        ID_CHANGE_CONFIG = 1004,
        ID_REPLACE_ALGORITHM = 1005,
        ID_AICHAT = 1006,
        ID_LOGFILE_PACK = 1007,
        ID_BUTTON_8 = 1008,
        ID_BUTTON_9 = 1009,
        ID_BUTTON_10 = 1010,
        ID_BUTTON_11 = 1011,
        ID_BUTTON_12 = 1012,
        ID_BUTTON_13 = 1013,
        ID_BUTTON_14 = 1014,
        ID_BUTTON_15 = 1015,
        ID_BUTTON_16 = 1016
    };

    wxDECLARE_EVENT_TABLE();
};

#endif // MAINFRAME_H
