#include "UdpTestDialog.h"
#include <wx/sizer.h>
#include <wx/statbox.h>

// 字节序转换函数：将主机字节序转换为大端模式（网络字节序）
static uint32_t HostToBigEndian32(uint32_t value)
{
    return ((value & 0xFF000000) >> 24) |
           ((value & 0x00FF0000) >> 8)  |
           ((value & 0x0000FF00) << 8)  |
           ((value & 0x000000FF) << 24);
}
#include <wx/msgdlg.h>
#include <cstring>

wxBEGIN_EVENT_TABLE(UdpTestDialog, wxDialog)
    EVT_BUTTON(ID_CONNECT_BTN, UdpTestDialog::OnConnect)
    EVT_BUTTON(ID_START_BTN, UdpTestDialog::OnStart)
    EVT_BUTTON(ID_STOP_BTN, UdpTestDialog::OnStop)
    EVT_BUTTON(ID_SINGLE_VALVE_BTN, UdpTestDialog::OnSingleValveTest)
    EVT_BUTTON(ID_CONTINUOUS_TEST_BTN, UdpTestDialog::OnContinuousTest)
    EVT_CLOSE(UdpTestDialog::OnClose)
    EVT_TIMER(ID_SEND_TIMER, UdpTestDialog::OnTimer)
    EVT_SOCKET(ID_SOCKET_EVENT, UdpTestDialog::OnSocketEvent)
    EVT_SPINCTRL(ID_VALVE_COUNT_SPIN, UdpTestDialog::OnValveCountChanged)
wxEND_EVENT_TABLE()

UdpTestDialog::UdpTestDialog(wxWindow* parent)
    : wxDialog(parent, wxID_ANY, wxT("阀组测试"), wxDefaultPosition, wxSize(1100, 800))
    , m_socket(nullptr)
    , m_connected(false)
    , m_sendTimer(nullptr)
    , m_isRunning(false)
    , m_totalPackets(0)
    , m_singleValveMode(false)
    , m_singleValveNumber(1)
    , m_singleValvePacketCount(0)
    , m_singleValveState(false)
    , m_continuousTestMode(false)
    , m_currentTestValve(1)
    , m_continuousPacketCount(0)
    , m_continuousValveState(false)
    , m_packetCount(0)
{
    CreateControls();
    
    // 创建定时器
    m_sendTimer = new wxTimer(this, ID_SEND_TIMER);
    
    // 设置默认值
    m_localIpText->SetValue(wxT("************"));
    m_localPortSpin->SetValue(1234);
    m_remoteIpText->SetValue(wxT("*************"));
    m_remotePortSpin->SetValue(1234);
    
    m_valveCountSpin->SetValue(90);
    m_blowDelaySpin->SetValue(500);
    m_triggerTimeSpin->SetValue(800);

    // 设置结束阀号的初始值和范围
    m_continuousEndValveSpin->SetValue(90);
    m_continuousEndValveSpin->SetRange(1, 560);

    UpdateStatus(wxT("未连接"));
}

UdpTestDialog::~UdpTestDialog()
{
    if (m_sendTimer) {
        m_sendTimer->Stop();
        delete m_sendTimer;
    }
    
    DisconnectSocket();
}

void UdpTestDialog::CreateControls()
{
    wxBoxSizer* mainSizer = new wxBoxSizer(wxVERTICAL);
    
    // 网络连接设置
    wxStaticBoxSizer* networkBox = new wxStaticBoxSizer(wxVERTICAL, this, wxT("网络连接"));

    // 本机设置行
    wxBoxSizer* localSizer = new wxBoxSizer(wxHORIZONTAL);
    localSizer->Add(new wxStaticText(this, wxID_ANY, wxT("本机IP:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_localIpText = new wxTextCtrl(this, wxID_ANY, wxEmptyString, wxDefaultPosition, wxSize(150, -1));
    localSizer->Add(m_localIpText, 0, wxRIGHT, 10);

    localSizer->Add(new wxStaticText(this, wxID_ANY, wxT("端口:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_localPortSpin = new wxSpinCtrl(this, wxID_ANY, wxEmptyString, wxDefaultPosition, wxSize(150, -1),
                                     wxSP_ARROW_KEYS, 1, 65535, 9020);
    localSizer->Add(m_localPortSpin, 0);

    networkBox->Add(localSizer, 0, wxEXPAND | wxALL, 5);

    // 远端设置行
    wxBoxSizer* remoteSizer = new wxBoxSizer(wxHORIZONTAL);
    remoteSizer->Add(new wxStaticText(this, wxID_ANY, wxT("远端IP:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_remoteIpText = new wxTextCtrl(this, wxID_ANY, wxEmptyString, wxDefaultPosition, wxSize(150, -1));
    remoteSizer->Add(m_remoteIpText, 0, wxRIGHT, 10);

    remoteSizer->Add(new wxStaticText(this, wxID_ANY, wxT("端口:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_remotePortSpin = new wxSpinCtrl(this, wxID_ANY, wxEmptyString, wxDefaultPosition, wxSize(150, -1),
                                      wxSP_ARROW_KEYS, 1, 65535, 9020);
    remoteSizer->Add(m_remotePortSpin, 0, wxRIGHT, 15);

    // 连接按钮和状态显示
    m_connectBtn = new wxButton(this, ID_CONNECT_BTN, wxT("连接"));
    remoteSizer->Add(m_connectBtn, 0, wxRIGHT, 10);

    m_statusText = new wxStaticText(this, wxID_ANY, wxT("未连接"));
    remoteSizer->Add(m_statusText, 1, wxALIGN_CENTER_VERTICAL);

    networkBox->Add(remoteSizer, 0, wxEXPAND | wxALL, 5);
    
    mainSizer->Add(networkBox, 0, wxEXPAND | wxALL, 5);
    
    // 控制参数设置
    wxStaticBoxSizer* controlBox = new wxStaticBoxSizer(wxVERTICAL, this, wxT("控制参数"));

    // 将三个参数放在一行中
    wxBoxSizer* paramSizer = new wxBoxSizer(wxHORIZONTAL);

    // 阀总数
    paramSizer->Add(new wxStaticText(this, wxID_ANY, wxT("阀总数:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_valveCountSpin = new wxSpinCtrl(this, ID_VALVE_COUNT_SPIN, wxEmptyString, wxDefaultPosition, wxSize(150, -1),
                                      wxSP_ARROW_KEYS, 1, 560, 90);
    paramSizer->Add(m_valveCountSpin, 0, wxRIGHT, 15);

    // 喷吹延时
    paramSizer->Add(new wxStaticText(this, wxID_ANY, wxT("喷吹延时(X*800us):")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_blowDelaySpin = new wxSpinCtrl(this, wxID_ANY, wxEmptyString, wxDefaultPosition, wxSize(150, -1),
                                     wxSP_ARROW_KEYS, 1, 1000, 1);
    paramSizer->Add(m_blowDelaySpin, 0, wxRIGHT, 15);

    // 行触发时间
    paramSizer->Add(new wxStaticText(this, wxID_ANY, wxT("行触发时间(us):")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_triggerTimeSpin = new wxSpinCtrl(this, wxID_ANY, wxEmptyString, wxDefaultPosition, wxSize(150, -1),
                                       wxSP_ARROW_KEYS, 1, 10000, 800);
    paramSizer->Add(m_triggerTimeSpin, 0, wxRIGHT, 15);

    // 触发方式
    paramSizer->Add(new wxStaticText(this, wxID_ANY, wxT("触发方式:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    wxArrayString triggerModeChoices;
    triggerModeChoices.Add(wxT("外触发"));
    triggerModeChoices.Add(wxT("内触发"));
    m_triggerModeChoice = new wxChoice(this, wxID_ANY, wxDefaultPosition, wxSize(150, -1), triggerModeChoices);
    m_triggerModeChoice->SetSelection(0);  // 默认选择外触发
    paramSizer->Add(m_triggerModeChoice, 0);

    controlBox->Add(paramSizer, 0, wxEXPAND | wxALL, 5);

    wxBoxSizer* buttonSizer = new wxBoxSizer(wxHORIZONTAL);
    m_startBtn = new wxButton(this, ID_START_BTN, wxT("开始"));
    m_stopBtn = new wxButton(this, ID_STOP_BTN, wxT("停止"));
    m_startBtn->Enable(false);
    m_stopBtn->Enable(false);
    
    buttonSizer->Add(m_startBtn, 0, wxRIGHT, 10);
    buttonSizer->Add(m_stopBtn, 0);
    
    controlBox->Add(buttonSizer, 0, wxALIGN_CENTER | wxALL, 5);

    mainSizer->Add(controlBox, 0, wxEXPAND | wxALL, 5);

    // 测试模式设置（单阀测试和连续测试在同一行）
    wxStaticBoxSizer* testModeBox = new wxStaticBoxSizer(wxVERTICAL, this, wxT("测试模式"));

    // 第一行：单阀测试和连续测试按钮
    wxBoxSizer* testButtonSizer = new wxBoxSizer(wxHORIZONTAL);

    // 单阀测试部分
    testButtonSizer->Add(new wxStaticText(this, wxID_ANY, wxT("测试阀号:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_singleValveSpin = new wxSpinCtrl(this, wxID_ANY, wxT("1"), wxDefaultPosition, wxSize(150, -1),
                                       wxSP_ARROW_KEYS, 1, 560, 1);
    testButtonSizer->Add(m_singleValveSpin, 0, wxRIGHT, 15);

    m_singleValveBtn = new wxButton(this, ID_SINGLE_VALVE_BTN, wxT("开始单阀测试"));
    m_singleValveBtn->Enable(false);
    testButtonSizer->Add(m_singleValveBtn, 0, wxRIGHT, 15);

    // 连续测试部分
    testButtonSizer->Add(new wxStaticText(this, wxID_ANY, wxT("起始阀号:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_continuousStartValveSpin = new wxSpinCtrl(this, wxID_ANY, wxT("1"), wxDefaultPosition, wxSize(150, -1),
                                               wxSP_ARROW_KEYS, 1, 560, 1);
    testButtonSizer->Add(m_continuousStartValveSpin, 0, wxRIGHT, 8);

    testButtonSizer->Add(new wxStaticText(this, wxID_ANY, wxT("结束阀号:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_continuousEndValveSpin = new wxSpinCtrl(this, wxID_ANY, wxT("90"), wxDefaultPosition, wxSize(150, -1),
                                             wxSP_ARROW_KEYS, 1, 560, 90);
    testButtonSizer->Add(m_continuousEndValveSpin, 0, wxRIGHT, 8);

    m_continuousTestBtn = new wxButton(this, ID_CONTINUOUS_TEST_BTN, wxT("开始连续测试"));
    m_continuousTestBtn->Enable(false);
    testButtonSizer->Add(m_continuousTestBtn, 0, wxRIGHT, 10);

    m_currentValveLabel = new wxStaticText(this, wxID_ANY, wxT("当前测试阀: 无"));
    testButtonSizer->Add(m_currentValveLabel, 1, wxALIGN_CENTER_VERTICAL);

    testModeBox->Add(testButtonSizer, 0, wxEXPAND | wxALL, 5);

    // 第二行：功能说明
    wxBoxSizer* descSizer = new wxBoxSizer(wxHORIZONTAL);

    wxStaticText* singleValveDesc = new wxStaticText(this, wxID_ANY,
        wxT("单阀测试：指定阀开启500ms，关闭500ms循环"));
    descSizer->Add(singleValveDesc, 1, wxRIGHT, 20);

    wxStaticText* continuousTestDesc = new wxStaticText(this, wxID_ANY,
        wxT("连续测试：从起始阀号到结束阀号依次测试"));
    descSizer->Add(continuousTestDesc, 1);

    testModeBox->Add(descSizer, 0, wxEXPAND | wxLEFT | wxRIGHT | wxBOTTOM, 5);

    mainSizer->Add(testModeBox, 0, wxEXPAND | wxALL, 5);

    // 状态显示区域
    wxStaticBoxSizer* statusBox = new wxStaticBoxSizer(wxVERTICAL, this, wxT("设备状态"));

    // 第一行：基本参数
    wxBoxSizer* statusRow1 = new wxBoxSizer(wxHORIZONTAL);
    statusRow1->Add(new wxStaticText(this, wxID_ANY, wxT("喷吹延时:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_statusBlowDelayLabel = new wxStaticText(this, wxID_ANY, wxT("--"), wxDefaultPosition, wxSize(120, -1));
    statusRow1->Add(m_statusBlowDelayLabel, 0, wxRIGHT, 20);

    statusRow1->Add(new wxStaticText(this, wxID_ANY, wxT("触发时间:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_statusTriggerTimeLabel = new wxStaticText(this, wxID_ANY, wxT("--"), wxDefaultPosition, wxSize(100, -1));
    statusRow1->Add(m_statusTriggerTimeLabel, 0, wxRIGHT, 20);

    statusRow1->Add(new wxStaticText(this, wxID_ANY, wxT("阀执行频率:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_statusValveFreqLabel = new wxStaticText(this, wxID_ANY, wxT("--"), wxDefaultPosition, wxSize(80, -1));
    statusRow1->Add(m_statusValveFreqLabel, 0);

    statusBox->Add(statusRow1, 0, wxEXPAND | wxALL, 5);

    // 第二行：触发信号统计
    wxBoxSizer* statusRow2 = new wxBoxSizer(wxHORIZONTAL);
    statusRow2->Add(new wxStaticText(this, wxID_ANY, wxT("总触发信号:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_statusTotalTriggersLabel = new wxStaticText(this, wxID_ANY, wxT("--"), wxDefaultPosition, wxSize(80, -1));
    statusRow2->Add(m_statusTotalTriggersLabel, 0, wxRIGHT, 20);

    statusRow2->Add(new wxStaticText(this, wxID_ANY, wxT("丢失触发(<90ms):")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_statusLostTriggersALabel = new wxStaticText(this, wxID_ANY, wxT("--"), wxDefaultPosition, wxSize(80, -1));
    statusRow2->Add(m_statusLostTriggersALabel, 0, wxRIGHT, 20);

    statusRow2->Add(new wxStaticText(this, wxID_ANY, wxT("丢失触发(>110ms):")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_statusLostTriggersBLabel = new wxStaticText(this, wxID_ANY, wxT("--"), wxDefaultPosition, wxSize(80, -1));
    statusRow2->Add(m_statusLostTriggersBLabel, 0);

    statusBox->Add(statusRow2, 0, wxEXPAND | wxALL, 5);

    // 第三行：数据包统计
    wxBoxSizer* statusRow3 = new wxBoxSizer(wxHORIZONTAL);
    statusRow3->Add(new wxStaticText(this, wxID_ANY, wxT("总数据包:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_statusTotalPacketsLabel = new wxStaticText(this, wxID_ANY, wxT("--"), wxDefaultPosition, wxSize(80, -1));
    statusRow3->Add(m_statusTotalPacketsLabel, 0, wxRIGHT, 20);

    statusRow3->Add(new wxStaticText(this, wxID_ANY, wxT("丢失数据包(少):")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_statusLostPacketsALabel = new wxStaticText(this, wxID_ANY, wxT("--"), wxDefaultPosition, wxSize(80, -1));
    statusRow3->Add(m_statusLostPacketsALabel, 0, wxRIGHT, 20);

    statusRow3->Add(new wxStaticText(this, wxID_ANY, wxT("丢失数据包(多):")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_statusLostPacketsBLabel = new wxStaticText(this, wxID_ANY, wxT("--"), wxDefaultPosition, wxSize(80, -1));
    statusRow3->Add(m_statusLostPacketsBLabel, 0, wxRIGHT, 20);

    statusRow3->Add(new wxStaticText(this, wxID_ANY, wxT("总执行次数:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_statusTotalExecutionsLabel = new wxStaticText(this, wxID_ANY, wxT("--"), wxDefaultPosition, wxSize(100, -1));
    statusRow3->Add(m_statusTotalExecutionsLabel, 0);

    statusBox->Add(statusRow3, 0, wxEXPAND | wxALL, 5);

    mainSizer->Add(statusBox, 0, wxEXPAND | wxALL, 5);

    // 日志显示
    wxStaticBoxSizer* logBox = new wxStaticBoxSizer(wxVERTICAL, this, wxT("日志"));
    m_logText = new wxTextCtrl(this, wxID_ANY, wxEmptyString, wxDefaultPosition, wxSize(-1, 200),
                               wxTE_MULTILINE | wxTE_READONLY);
    logBox->Add(m_logText, 1, wxEXPAND | wxALL, 5);
    
    mainSizer->Add(logBox, 1, wxEXPAND | wxALL, 5);
    
    // 关闭按钮
    wxBoxSizer* closeSizer = new wxBoxSizer(wxHORIZONTAL);
    closeSizer->AddStretchSpacer();
    wxButton* closeBtn = new wxButton(this, wxID_CLOSE, wxT("关闭"));
    closeBtn->Bind(wxEVT_COMMAND_BUTTON_CLICKED, [this](wxCommandEvent&) {
        Close();
    });
    closeSizer->Add(closeBtn, 0);

    mainSizer->Add(closeSizer, 0, wxEXPAND | wxALL, 5);

    SetSizer(mainSizer);

    // 设置最小窗口尺寸（增加高度以容纳状态显示区域）
    SetMinSize(wxSize(1100, 900));
}

void UdpTestDialog::OnConnect(wxCommandEvent& WXUNUSED(event))
{
    if (!m_connected) {
        if (ConnectSocket()) {
            m_connectBtn->SetLabel(wxT("断开"));
            m_startBtn->Enable(true);
            m_singleValveBtn->Enable(true);
            m_continuousTestBtn->Enable(true);
            UpdateStatus(wxT("已连接"));
            LogMessage(wxT("UDP连接成功"));
        } else {
            UpdateStatus(wxT("连接失败"));
            LogMessage(wxT("UDP连接失败"));
        }
    } else {
        DisconnectSocket();
        m_connectBtn->SetLabel(wxT("连接"));
        m_startBtn->Enable(false);
        m_stopBtn->Enable(false);
        m_singleValveBtn->Enable(false);
        m_continuousTestBtn->Enable(false);
        UpdateStatus(wxT("已断开"));
        LogMessage(wxT("UDP连接已断开"));
    }
}

void UdpTestDialog::OnStart(wxCommandEvent& WXUNUSED(event))
{
    if (!m_connected) {
        wxMessageBox(wxT("请先连接UDP"), wxT("错误"), wxOK | wxICON_ERROR);
        return;
    }

    m_isRunning = true;
    m_totalPackets = CalculateTotalPackets(m_valveCountSpin->GetValue());
    m_packetCount = 0;  // 重置包计数器

    m_startBtn->Enable(false);
    m_stopBtn->Enable(true);

    LogMessage(wxString::Format(wxT("开始发送数据，阀个数: %d, 总包数: %d"),
                               m_valveCountSpin->GetValue(), m_totalPackets));

    // 发送控制包（使能置1）
    SendControlPacket(true);

    // 开始定时发送阀数据包（100ms间隔）
    m_sendTimer->Start(100);
}

void UdpTestDialog::OnStop(wxCommandEvent& WXUNUSED(event))
{
    LogMessage(wxT("正在停止数据发送，发送关闭指令..."));

    // 先发送一轮关闭指令（所有阀都关闭）
    if (m_singleValveMode) {
        // 单阀测试模式：发送关闭指令
        for (int i = 1; i <= m_totalPackets; i++) {
            m_packetCount++;  // 发送停止数据包时也要递增序号
            SendSingleValveClosePacket(i);
        }
    } else if (m_continuousTestMode) {
        // 连续测试模式：发送关闭指令
        for (int i = 1; i <= m_totalPackets; i++) {
            m_packetCount++;  // 发送停止数据包时也要递增序号
            SendSingleValveClosePacket(i);
        }
    } else {
        // 正常模式：发送关闭指令
        for (int i = 1; i <= m_totalPackets; i++) {
            m_packetCount++;  // 发送停止数据包时也要递增序号
            SendValveClosePacket(i);
        }
    }

    m_isRunning = false;
    m_singleValveMode = false;
    m_continuousTestMode = false;
    m_sendTimer->Stop();

    m_startBtn->Enable(true);
    m_stopBtn->Enable(false);
    m_singleValveBtn->Enable(true);
    m_singleValveBtn->SetLabel(wxT("开始单阀测试"));
    m_continuousTestBtn->Enable(true);
    m_continuousTestBtn->SetLabel(wxT("开始连续测试"));
    m_currentValveLabel->SetLabel(wxT("当前测试阀: 无"));

    // 发送控制包（使能置0）
    SendControlPacket(false);

    LogMessage(wxT("数据发送已停止"));
}

void UdpTestDialog::OnSingleValveTest(wxCommandEvent& WXUNUSED(event))
{
    if (!m_connected) {
        wxMessageBox(wxT("请先连接UDP"), wxT("错误"), wxOK | wxICON_ERROR);
        return;
    }

    if (!m_singleValveMode) {
        // 开始单阀测试
        m_singleValveMode = true;
        m_isRunning = true;
        m_singleValveNumber = m_singleValveSpin->GetValue();
        m_singleValvePacketCount = 0;
        m_singleValveState = true; // 开始时阀打开
        m_totalPackets = CalculateTotalPackets(m_valveCountSpin->GetValue());
        m_packetCount = 0;  // 重置包计数器

        m_startBtn->Enable(false);
        m_stopBtn->Enable(true);
        m_singleValveBtn->SetLabel(wxT("停止单阀测试"));

        LogMessage(wxString::Format(wxT("开始单阀测试，阀号: %d"), m_singleValveNumber));

        // 发送控制包（使能置1）
        SendControlPacket(true);

        // 开始定时发送阀数据包（100ms间隔）
        m_sendTimer->Start(100);
    } else {
        // 停止单阀测试
        LogMessage(wxT("正在停止单阀测试，发送关闭指令..."));

        // 先发送一轮关闭指令（所有阀都关闭）
        m_packetCount++;  // 发送停止数据包时也要递增序号
        for (int i = 1; i <= m_totalPackets; i++) {
            SendSingleValveClosePacket(i);
        }

        m_singleValveMode = false;
        m_isRunning = false;
        m_sendTimer->Stop();

        m_startBtn->Enable(true);
        m_stopBtn->Enable(false);
        m_singleValveBtn->Enable(true);
        m_singleValveBtn->SetLabel(wxT("开始单阀测试"));

        // 发送控制包（使能置0）
        SendControlPacket(false);

        LogMessage(wxT("单阀测试已停止"));
    }
}

void UdpTestDialog::OnContinuousTest(wxCommandEvent& WXUNUSED(event))
{
    if (!m_connected) {
        wxMessageBox(wxT("请先连接UDP"), wxT("错误"), wxOK | wxICON_ERROR);
        return;
    }

    if (!m_continuousTestMode) {
        // 参数验证
        int startValve = m_continuousStartValveSpin->GetValue();
        int endValve = m_continuousEndValveSpin->GetValue();

        if (endValve < startValve) {
            wxMessageBox(wxT("结束阀号不能小于起始阀号！"), wxT("参数错误"), wxOK | wxICON_ERROR);
            return;
        }

        if (startValve < 1 || endValve > 560) {
            wxMessageBox(wxT("阀号范围必须在1-560之间！"), wxT("参数错误"), wxOK | wxICON_ERROR);
            return;
        }

        // 开始连续测试
        m_continuousTestMode = true;
        m_isRunning = true;
        m_currentTestValve = startValve; // 从用户指定的起始阀开始
        m_continuousPacketCount = 0;
        m_continuousValveState = true; // 开始时阀打开
        m_totalPackets = CalculateTotalPackets(m_valveCountSpin->GetValue());
        m_packetCount = 0;  // 重置包计数器

        m_startBtn->Enable(false);
        m_stopBtn->Enable(true);
        m_singleValveBtn->Enable(false);
        m_continuousTestBtn->SetLabel(wxT("停止连续测试"));

        LogMessage(wxString::Format(wxT("开始连续测试，测试范围: 阀%d到阀%d"),
                                   startValve, endValve));
        m_currentValveLabel->SetLabel(wxString::Format(wxT("当前测试阀: %d"), m_currentTestValve));

        // 发送控制包（使能置1）
        SendControlPacket(true);

        // 开始定时发送阀数据包（100ms间隔）
        m_sendTimer->Start(100);
    } else {
        // 停止连续测试
        LogMessage(wxT("正在停止连续测试，发送关闭指令..."));

        // 先发送一轮关闭指令（所有阀都关闭）
        m_packetCount++;  // 发送停止数据包时也要递增序号
        for (int i = 1; i <= m_totalPackets; i++) {
            SendSingleValveClosePacket(i);
        }

        m_continuousTestMode = false;
        m_isRunning = false;
        m_sendTimer->Stop();

        m_startBtn->Enable(true);
        m_stopBtn->Enable(false);
        m_singleValveBtn->Enable(true);
        m_continuousTestBtn->Enable(true);
        m_continuousTestBtn->SetLabel(wxT("开始连续测试"));
        m_currentValveLabel->SetLabel(wxT("当前测试阀: 无"));

        // 发送控制包（使能置0）
        SendControlPacket(false);

        LogMessage(wxT("连续测试已停止"));
    }
}

void UdpTestDialog::OnClose(wxCloseEvent& event)
{
    if (m_isRunning) {
        m_isRunning = false;
        m_sendTimer->Stop();

        m_startBtn->Enable(true);
        m_stopBtn->Enable(false);

        // 发送控制包（使能置0）
        SendControlPacket(false);

        LogMessage(wxT("停止发送数据"));
    }
    DisconnectSocket();
    event.Skip();
}

void UdpTestDialog::OnTimer(wxTimerEvent& WXUNUSED(event))
{
    if (!m_isRunning || !m_connected) {
        m_sendTimer->Stop();
        return;
    }
    m_packetCount++;
    if (m_singleValveMode) {
        // 单阀测试模式
        m_singleValvePacketCount++;

        // 每5包（500ms）切换一次阀状态
        // 行触发时间 * 125 = 每包执行时间
        // 例如：800us * 125 = 100ms，所以5包 = 500ms
        int packetsFor500ms = 500000 / (m_triggerTimeSpin->GetValue() * 125); // 500ms转换为us

        if (m_singleValvePacketCount >= packetsFor500ms) {
            m_singleValveState = !m_singleValveState; // 切换状态
            m_singleValvePacketCount = 0;

            LogMessage(wxString::Format(wxT("阀%d状态切换为: %s"),
                                       m_singleValveNumber,
                                       m_singleValveState ? wxT("开启") : wxT("关闭")));
        }

        // 在100ms周期内发送所有数据包
        for (int i = 1; i <= m_totalPackets; i++) {
            SendSingleValveDataPacket(i); // 发送单阀测试数据包
        }

        LogMessage(wxString::Format(wxT("发送单阀测试数据包 %d(共%d包), 阀%d: %s"),
                                   m_packetCount,m_totalPackets, m_singleValveNumber,
                                   m_singleValveState ? wxT("开启") : wxT("关闭")));
    } else if (m_continuousTestMode) {
        // 连续测试模式
        m_continuousPacketCount++;

        // 每5包（500ms）切换一次阀状态
        int packetsFor500ms = 500000 / (m_triggerTimeSpin->GetValue() * 125); // 500ms转换为us

        if (m_continuousPacketCount >= packetsFor500ms) {
            if (m_continuousValveState) {
                // 当前阀从开启变为关闭
                m_continuousValveState = false;
                LogMessage(wxString::Format(wxT("阀%d关闭"), m_currentTestValve));
            } else {
                // 当前阀从关闭变为开启，准备切换到下一个阀
                m_continuousValveState = true;
                m_currentTestValve++;

                // 检查是否已经测试完所有阀
                int startValve = m_continuousStartValveSpin->GetValue();
                int endValve = m_continuousEndValveSpin->GetValue();

                if (m_currentTestValve > endValve) {
                    // 所有阀测试完成，停止连续测试
                    LogMessage(wxString::Format(wxT("所有阀测试完成（阀%d到阀%d），停止连续测试"),
                                               startValve, endValve));

                    // 发送关闭指令
                    for (int i = 1; i <= m_totalPackets; i++) {
                        m_packetCount++;  // 发送停止数据包时也要递增序号
                        SendSingleValveClosePacket(i);
                    }

                    m_continuousTestMode = false;
                    m_isRunning = false;
                    m_sendTimer->Stop();

                    m_startBtn->Enable(true);
                    m_stopBtn->Enable(false);
                    m_singleValveBtn->Enable(true);
                    m_continuousTestBtn->Enable(true);
                    m_continuousTestBtn->SetLabel(wxT("开始连续测试"));
                    m_currentValveLabel->SetLabel(wxT("当前测试阀: 无"));

                    // 发送控制包（使能置0）
                    SendControlPacket(false);
                    return;
                }

                LogMessage(wxString::Format(wxT("切换到阀%d开启"), m_currentTestValve));
                m_currentValveLabel->SetLabel(wxString::Format(wxT("当前测试阀: %d"), m_currentTestValve));
            }
            m_continuousPacketCount = 0;
        }

        // 在100ms周期内发送所有数据包
        for (int i = 1; i <= m_totalPackets; i++) {
            SendContinuousValveDataPacket(i); // 发送连续测试数据包
        }

        LogMessage(wxString::Format(wxT("发送连续测试数据包 %d(共%d包), 阀%d: %s"),
                                   m_packetCount,m_totalPackets, m_currentTestValve,
                                   m_continuousValveState ? wxT("开启") : wxT("关闭")));
    } else {
        // 正常模式
        // 在100ms周期内发送所有数据包
        for (int i = 1; i <= m_totalPackets; i++) {
            SendValveDataPacket(i); // 包序号从1开始
        }

        LogMessage(wxString::Format(wxT("发送完成一轮数据包 (共%d包)"), m_totalPackets));
    }
}

void UdpTestDialog::OnSocketEvent(wxSocketEvent& event)
{
    switch (event.GetSocketEvent()) {
        case wxSOCKET_INPUT:
            {
                // 处理接收到的数据
                uint8_t buffer[1500];  // UDP最大数据包大小
                wxIPV4address addr;

                m_socket->RecvFrom(addr, buffer, sizeof(buffer));
                size_t bytesRead = m_socket->LastCount();

                if (bytesRead > 0) {
                    LogMessage(wxString::Format(wxT("收到数据包，大小: %zu 字节"), bytesRead));

                    // 解析数据包
                    if (bytesRead >= 4) {
                        uint32_t packetType = *((uint32_t*)buffer);
                        // 转换字节序（假设接收到的是网络字节序）
                        packetType = wxUINT32_SWAP_ON_LE(packetType);

                        if (packetType == 0x58) {
                            // 状态数据包
                            ParseStatusPacket(buffer, bytesRead);
                        } else {
                            LogMessage(wxString::Format(wxT("收到其他类型数据包: 0x%08X"), packetType));
                        }
                    }
                }
            }
            break;
        case wxSOCKET_LOST:
            LogMessage(wxT("连接丢失"));
            DisconnectSocket();
            break;
        default:
            break;
    }
}

void UdpTestDialog::UpdateStatus(const wxString& status)
{
    m_statusText->SetLabel(status);
}

void UdpTestDialog::LogMessage(const wxString& message)
{
    wxDateTime now = wxDateTime::Now();
    wxString timeStr = now.Format(wxT("%H:%M:%S"));

    // 获取毫秒部分
    wxLongLong nowMs = wxGetLocalTimeMillis();
    int milliseconds = (nowMs % 1000).ToLong();

    // 格式化为 HH:MM:SS.mmm
    wxString timeWithMs = wxString::Format(wxT("%s.%03d"), timeStr, milliseconds);

    m_logText->AppendText(wxString::Format(wxT("[%s] %s\n"), timeWithMs, message));

    // 限制日志行数，防止内存占用过多
    const int MAX_LOG_LINES = 1000;  // 最大保留1000行日志
    int lineCount = m_logText->GetNumberOfLines();

    if (lineCount > MAX_LOG_LINES) {
        // 删除前面的行，保留最新的日志
        int linesToRemove = lineCount - MAX_LOG_LINES + 100;  // 一次删除多一些，减少频繁操作

        long pos = 0;
        for (int i = 0; i < linesToRemove; i++) {
            pos = m_logText->GetValue().find('\n', pos);
            if (pos == wxString::npos) break;
            pos++;
        }

        if (pos != wxString::npos && pos > 0) {
            m_logText->Remove(0, pos);
        }
    }
}

bool UdpTestDialog::ConnectSocket()
{
    if (m_socket) {
        DisconnectSocket();
    }

    // 设置本地地址
    if (!m_localAddr.Hostname(m_localIpText->GetValue()) ||
        !m_localAddr.Service(m_localPortSpin->GetValue())) {
        LogMessage(wxT("本地地址设置失败"));
        return false;
    }

    // 设置远程地址
    if (!m_remoteAddr.Hostname(m_remoteIpText->GetValue()) ||
        !m_remoteAddr.Service(m_remotePortSpin->GetValue())) {
        LogMessage(wxT("远程地址设置失败"));
        return false;
    }

    // 创建UDP socket
    m_socket = new wxDatagramSocket(m_localAddr, wxSOCKET_NOWAIT);
    if (!m_socket->IsOk()) {
        LogMessage(wxT("创建UDP socket失败"));
        delete m_socket;
        m_socket = nullptr;
        return false;
    }

    // 设置事件处理
    m_socket->SetEventHandler(*this, ID_SOCKET_EVENT);
    m_socket->SetNotify(wxSOCKET_INPUT_FLAG | wxSOCKET_LOST_FLAG);
    m_socket->Notify(true);

    m_connected = true;
    return true;
}

void UdpTestDialog::DisconnectSocket()
{
    if (m_socket) {
        m_socket->Destroy();
        m_socket = nullptr;
    }
    m_connected = false;
}

int UdpTestDialog::CalculateTotalPackets(int valveCount)
{
    // 根据协议：阀位数1-80: 1个数据包，81-160: 2个数据包，以此类推
    return (valveCount + 79) / 80;
}

void UdpTestDialog::SendControlPacket(bool enable)
{
    if (!m_socket || !m_connected) {
        return;
    }

    ControlPacket packet;
    memset(&packet, 0, sizeof(packet));

    // 设置数据（主机字节序）
    uint32_t packetType = 0;  // 0表示采集控制包
    uint32_t enableValue = enable ? 1 : 0;  // 1表示开始，0表示停止
    uint32_t blowDelay = m_blowDelaySpin->GetValue();  // 喷吹延时
    uint32_t triggerTime = m_triggerTimeSpin->GetValue();  // 行触发时间
    uint32_t totalPackets = m_totalPackets;  // 数据总包数
    uint32_t fpgaTriggerTime = 0;  // 暂未使用
    uint32_t fpgaTriggerMode = m_triggerModeChoice->GetSelection() == 0 ? 0 : 1;  // 0：外触发，1：内触发
    uint32_t fpgaDutyCycle = 0;  // 保留

    // 转换为大端模式（网络字节序）
    packet.packetType = HostToBigEndian32(packetType);
    packet.enable = HostToBigEndian32(enableValue);
    packet.blowDelay = HostToBigEndian32(blowDelay);
    packet.triggerTime = HostToBigEndian32(triggerTime);
    packet.totalPackets = HostToBigEndian32(totalPackets);
    packet.fpgaTriggerTime = HostToBigEndian32(fpgaTriggerTime);
    packet.fpgaTriggerMode = HostToBigEndian32(fpgaTriggerMode);
    packet.fpgaDutyCycle = HostToBigEndian32(fpgaDutyCycle);

    // 发送数据包
    m_socket->SendTo(m_remoteAddr, &packet, sizeof(packet));

    wxString triggerModeStr = m_triggerModeChoice->GetSelection() == 0 ? wxT("外触发") : wxT("内触发");
    LogMessage(wxString::Format(wxT("发送控制包: %s, 阀数: %d, 包数: %d, 触发方式: %s"),
                               enable ? wxT("开始") : wxT("停止"),
                               m_valveCountSpin->GetValue(), m_totalPackets, triggerModeStr));
}

void UdpTestDialog::SendValveDataPacket(int packetIndex)
{
    if (!m_socket || !m_connected || packetIndex < 1 || packetIndex > m_totalPackets) {
        return;
    }

    ValveDataPacket packet;
    memset(&packet, 0, sizeof(packet));

    // 计算当前包的阀范围
    int valveStart = (packetIndex - 1) * 80 + 1;  // 从1开始
    int valveEnd = wxMin(packetIndex * 80, m_valveCountSpin->GetValue());
    int valveCount = valveEnd - valveStart + 1;

    // 设置数据（主机字节序）
    uint32_t packetType = packetIndex;  // 1-7表示第几包阀数据
    uint32_t packetSize = 32 + valveCount * 16;  // 32字节头部 + 16字节*阀数量
    uint32_t blowDelay = m_blowDelaySpin->GetValue();
    uint32_t triggerTime = m_triggerTimeSpin->GetValue();
    uint32_t totalPackets = m_totalPackets;

    // 转换为大端模式（网络字节序）
    packet.packetType = HostToBigEndian32(packetType);
    packet.packetSize = HostToBigEndian32(packetSize);
    packet.blowDelay = HostToBigEndian32(blowDelay);
    packet.triggerTime = HostToBigEndian32(triggerTime);
    packet.totalPackets = HostToBigEndian32(totalPackets);
    packet.udp_send_num = HostToBigEndian32(m_packetCount);

    // 生成阀数据
    GenerateValveData(packet.valveData, valveStart, valveCount);

    // 发送数据包（只发送实际需要的字节数）
    size_t sendSize = 32 + valveCount * 16;  // 头部32字节 + 阀数据
    m_socket->SendTo(m_remoteAddr, &packet, sendSize);

    LogMessage(wxString::Format(wxT("发送第%d包阀数据: 阀%d-%d (%d个阀), UDP序号: %d"),
                               packetIndex, valveStart, valveEnd, valveCount, m_packetCount));
}

void UdpTestDialog::SendValveClosePacket(int packetIndex)
{
    if (!m_socket || !m_connected || packetIndex < 1 || packetIndex > m_totalPackets) {
        return;
    }

    ValveDataPacket packet;
    memset(&packet, 0, sizeof(packet));

    // 计算当前包的阀范围
    int valveStart = (packetIndex - 1) * 80 + 1;  // 从1开始
    int valveEnd = wxMin(packetIndex * 80, m_valveCountSpin->GetValue());
    int valveCount = valveEnd - valveStart + 1;

    // 设置数据（主机字节序）
    uint32_t packetType = packetIndex;  // 1-7表示第几包阀数据
    uint32_t packetSize = 32 + valveCount * 16;  // 32字节头部 + 16字节*阀数量
    uint32_t blowDelay = m_blowDelaySpin->GetValue();
    uint32_t triggerTime = m_triggerTimeSpin->GetValue();
    uint32_t totalPackets = m_totalPackets;

    // 转换为大端模式（网络字节序）
    packet.packetType = HostToBigEndian32(packetType);
    packet.packetSize = HostToBigEndian32(packetSize);
    packet.blowDelay = HostToBigEndian32(blowDelay);
    packet.triggerTime = HostToBigEndian32(triggerTime);
    packet.totalPackets = HostToBigEndian32(totalPackets);
    packet.udp_send_num = HostToBigEndian32(m_packetCount);

    // 生成关闭数据（所有阀都关闭）
    for (int valve = 0; valve < valveCount; valve++) {
        uint8_t* valveData = packet.valveData + valve * 16;
        memset(valveData, 0, 16);  // 所有阀都设为关闭状态（全0）
    }

    // 发送数据包（只发送实际需要的字节数）
    size_t sendSize = 32 + valveCount * 16;  // 头部32字节 + 阀数据
    m_socket->SendTo(m_remoteAddr, &packet, sendSize);
}

void UdpTestDialog::SendSingleValveDataPacket(int packetIndex)
{
    if (!m_socket || !m_connected || packetIndex < 1 || packetIndex > m_totalPackets) {
        return;
    }

    ValveDataPacket packet;
    memset(&packet, 0, sizeof(packet));

    // 计算当前包的阀范围
    int valveStart = (packetIndex - 1) * 80 + 1;  // 从1开始
    int valveEnd = wxMin(packetIndex * 80, m_valveCountSpin->GetValue());
    int valveCount = valveEnd - valveStart + 1;

    // 设置数据（主机字节序）
    uint32_t packetType = packetIndex;  // 1-7表示第几包阀数据
    uint32_t packetSize = 32 + valveCount * 16;  // 32字节头部 + 16字节*阀数量
    uint32_t blowDelay = m_blowDelaySpin->GetValue();
    uint32_t triggerTime = m_triggerTimeSpin->GetValue();
    uint32_t totalPackets = m_totalPackets;

    // 转换为大端模式（网络字节序）
    packet.packetType = HostToBigEndian32(packetType);
    packet.packetSize = HostToBigEndian32(packetSize);
    packet.blowDelay = HostToBigEndian32(blowDelay);
    packet.triggerTime = HostToBigEndian32(triggerTime);
    packet.totalPackets = HostToBigEndian32(totalPackets);
    packet.udp_send_num = HostToBigEndian32(m_packetCount);

    // 生成单阀测试数据
    GenerateSingleValveData(packet.valveData, valveStart, valveCount, m_singleValveNumber, m_singleValveState);

    // 发送数据包（只发送实际需要的字节数）
    size_t sendSize = 32 + valveCount * 16;  // 头部32字节 + 阀数据
    m_socket->SendTo(m_remoteAddr, &packet, sendSize);
}

void UdpTestDialog::SendSingleValveClosePacket(int packetIndex)
{
    if (!m_socket || !m_connected || packetIndex < 1 || packetIndex > m_totalPackets) {
        return;
    }

    ValveDataPacket packet;
    memset(&packet, 0, sizeof(packet));

    // 计算当前包的阀范围
    int valveStart = (packetIndex - 1) * 80 + 1;  // 从1开始
    int valveEnd = wxMin(packetIndex * 80, m_valveCountSpin->GetValue());
    int valveCount = valveEnd - valveStart + 1;

    // 设置数据（主机字节序）
    uint32_t packetType = packetIndex;  // 1-7表示第几包阀数据
    uint32_t packetSize = 32 + valveCount * 16;  // 32字节头部 + 16字节*阀数量
    uint32_t blowDelay = m_blowDelaySpin->GetValue();
    uint32_t triggerTime = m_triggerTimeSpin->GetValue();
    uint32_t totalPackets = m_totalPackets;

    // 转换为大端模式（网络字节序）
    packet.packetType = HostToBigEndian32(packetType);
    packet.packetSize = HostToBigEndian32(packetSize);
    packet.blowDelay = HostToBigEndian32(blowDelay);
    packet.triggerTime = HostToBigEndian32(triggerTime);
    packet.totalPackets = HostToBigEndian32(totalPackets);
    packet.udp_send_num = HostToBigEndian32(m_packetCount);

    // 生成关闭数据（所有阀都关闭）
    for (int valve = 0; valve < valveCount; valve++) {
        uint8_t* valveData = packet.valveData + valve * 16;
        memset(valveData, 0, 16);  // 所有阀都设为关闭状态（全0）
    }

    // 发送数据包（只发送实际需要的字节数）
    size_t sendSize = 32 + valveCount * 16;  // 头部32字节 + 阀数据
    m_socket->SendTo(m_remoteAddr, &packet, sendSize);
}

void UdpTestDialog::GenerateValveData(uint8_t* data, int WXUNUSED(valveStart), int valveCount)
{
    // 每个阀16字节，前125个bit有效，后3个bit为0
    // 这里生成测试数据：简单的开关模式

    for (int valve = 0; valve < valveCount; valve++) {
        uint8_t* valveData = data + valve * 16;

        // 清零16字节
        memset(valveData, 0, 16);

        // 生成测试模式：前几个字节设置一些位
        // 这里可以根据实际需求修改数据生成逻辑

        // 示例：设置一些测试位模式
        // 前125位有效，按字节设置
        for (int byteIdx = 0; byteIdx < 15; byteIdx++) {  // 15字节 = 120位
            // 简单的测试模式：交替的开关模式
            if (byteIdx < 8) {
                valveData[byteIdx] = 0xAA;  // 10101010 模式
            } else {
                valveData[byteIdx] = 0x55;  // 01010101 模式
            }
        }

        // 最后一个字节只有5位有效（125 - 120 = 5位）
        valveData[15] = 0x1F & 0xF8;  // 只保留前5位，后3位清零
    }
}

void UdpTestDialog::GenerateSingleValveData(uint8_t* data, int valveStart, int valveCount, int targetValve, bool valveState)
{
    // 每个阀16字节，前125个bit有效，后3个bit为0
    // 单阀测试：只有指定的阀设置为开启或关闭状态，其他阀保持关闭

    for (int valve = 0; valve < valveCount; valve++) {
        uint8_t* valveData = data + valve * 16;
        int currentValveNumber = valveStart + valve;

        // 清零16字节
        memset(valveData, 0, 16);

        // 检查是否是目标阀
        if (currentValveNumber == targetValve) {
            if (valveState) {
                // 阀开启：所有16字节都设为0xFF（全1）
                // 但要注意最后一个字节只有前5位有效
                for (int byteIdx = 0; byteIdx < 15; byteIdx++) {
                    valveData[byteIdx] = 0xFF;
                }
                // 最后一个字节只有前5位有效（125 - 120 = 5位）
                valveData[15] = 0xF8;  // 11111000，前5位为1，后3位为0
            }
            // 如果valveState为false，数据已经在memset中清零了
        }
        // 其他阀保持关闭状态（已在memset中清零）
    }
}

void UdpTestDialog::SendContinuousValveDataPacket(int packetIndex)
{
    if (!m_socket || !m_connected || packetIndex < 1 || packetIndex > m_totalPackets) {
        return;
    }

    ValveDataPacket packet;
    memset(&packet, 0, sizeof(packet));

    // 计算当前包的阀范围
    int valveStart = (packetIndex - 1) * 80 + 1;  // 从1开始
    int valveEnd = wxMin(packetIndex * 80, m_valveCountSpin->GetValue());
    int valveCount = valveEnd - valveStart + 1;

    // 设置数据（主机字节序）
    uint32_t packetType = packetIndex;  // 1-7表示第几包阀数据
    uint32_t packetSize = 32 + valveCount * 16;  // 32字节头部 + 16字节*阀数量
    uint32_t blowDelay = m_blowDelaySpin->GetValue();
    uint32_t triggerTime = m_triggerTimeSpin->GetValue();
    uint32_t totalPackets = m_totalPackets;

    // 转换为大端模式（网络字节序）
    packet.packetType = HostToBigEndian32(packetType);
    packet.packetSize = HostToBigEndian32(packetSize);
    packet.blowDelay = HostToBigEndian32(blowDelay);
    packet.triggerTime = HostToBigEndian32(triggerTime);
    packet.totalPackets = HostToBigEndian32(totalPackets);
    packet.udp_send_num = HostToBigEndian32(m_packetCount);

    // 生成连续测试数据
    GenerateContinuousValveData(packet.valveData, valveStart, valveCount, m_currentTestValve, m_continuousValveState);

    // 发送数据包（只发送实际需要的字节数）
    size_t sendSize = 32 + valveCount * 16;  // 头部32字节 + 阀数据
    m_socket->SendTo(m_remoteAddr, &packet, sendSize);
}

void UdpTestDialog::GenerateContinuousValveData(uint8_t* data, int valveStart, int valveCount, int targetValve, bool valveState)
{
    // 每个阀16字节，前125个bit有效，后3个bit为0
    // 连续测试：只有当前测试的阀设置为开启或关闭状态，其他阀保持关闭

    for (int valve = 0; valve < valveCount; valve++) {
        uint8_t* valveData = data + valve * 16;
        int currentValveNumber = valveStart + valve;

        // 清零16字节
        memset(valveData, 0, 16);

        // 检查是否是当前测试的阀
        if (currentValveNumber == targetValve) {
            if (valveState) {
                // 阀开启：所有16字节都设为0xFF（全1）
                // 但要注意最后一个字节只有前5位有效
                for (int byteIdx = 0; byteIdx < 15; byteIdx++) {
                    valveData[byteIdx] = 0xFF;
                }
                // 最后一个字节只有前5位有效（125 - 120 = 5位）
                valveData[15] = 0xF8;  // 11111000，前5位为1，后3位为0
            }
            // 如果valveState为false，数据已经在memset中清零了
        }
        // 其他阀保持关闭状态（已在memset中清零）
    }
}

void UdpTestDialog::OnValveCountChanged(wxSpinEvent& WXUNUSED(event))
{
    // 当阀总数改变时，更新结束阀号的默认值
    int valveCount = m_valveCountSpin->GetValue();

    // 如果当前结束阀号大于新的阀总数，则更新为阀总数
    if (m_continuousEndValveSpin->GetValue() > valveCount) {
        m_continuousEndValveSpin->SetValue(valveCount);
    }

    // 更新结束阀号的最大值
    m_continuousEndValveSpin->SetRange(1, valveCount);
}

void UdpTestDialog::ParseStatusPacket(const uint8_t* data, size_t dataSize)
{
    if (dataSize < sizeof(StatusPacket)) {
        LogMessage(wxString::Format(wxT("状态数据包大小不正确: %zu 字节，期望: %zu 字节"),
                                   dataSize, sizeof(StatusPacket)));
        return;
    }

    // 解析状态数据包
    StatusPacket status;
    memcpy(&status, data, sizeof(StatusPacket));

    // 转换字节序（假设接收到的是网络字节序，需要转换为主机字节序）
    status.packetType = wxUINT32_SWAP_ON_LE(status.packetType);
    status.blowDelay = wxUINT32_SWAP_ON_LE(status.blowDelay);
    status.triggerTime = wxUINT32_SWAP_ON_LE(status.triggerTime);
    status.totalTriggerSignals = wxUINT32_SWAP_ON_LE(status.totalTriggerSignals);
    status.lostTriggerSignalsA = wxUINT32_SWAP_ON_LE(status.lostTriggerSignalsA);
    status.totalDataPackets = wxUINT32_SWAP_ON_LE(status.totalDataPackets);
    status.lostDataPacketsA = wxUINT32_SWAP_ON_LE(status.lostDataPacketsA);
    status.valveFrequency = wxUINT32_SWAP_ON_LE(status.valveFrequency);
    status.totalValveExecutions = wxUINT32_SWAP_ON_LE(status.totalValveExecutions);
    status.lostTriggerSignalsB = wxUINT32_SWAP_ON_LE(status.lostTriggerSignalsB);
    status.lostDataPacketsB = wxUINT32_SWAP_ON_LE(status.lostDataPacketsB);
    status.reserved = wxUINT32_SWAP_ON_LE(status.reserved);

    // 验证包类型
    if (status.packetType != 0x58) {
        LogMessage(wxString::Format(wxT("状态数据包类型错误: 0x%08X，期望: 0x58"), status.packetType));
        return;
    }

    // 更新状态显示
    UpdateStatusDisplay(status);

    LogMessage(wxT("收到设备状态数据包"));
}

void UdpTestDialog::UpdateStatusDisplay(const StatusPacket& status)
{
    // 更新基本参数
    m_statusBlowDelayLabel->SetLabel(wxString::Format(wxT("%u (%.1fms)"),
                                                     status.blowDelay, status.blowDelay * 0.8));
    m_statusTriggerTimeLabel->SetLabel(wxString::Format(wxT("%u us"), status.triggerTime));
    m_statusValveFreqLabel->SetLabel(wxString::Format(wxT("%u /秒"), status.valveFrequency));

    // 更新触发信号统计
    m_statusTotalTriggersLabel->SetLabel(wxString::Format(wxT("%u"), status.totalTriggerSignals));
    m_statusLostTriggersALabel->SetLabel(wxString::Format(wxT("%u"), status.lostTriggerSignalsA));
    m_statusLostTriggersBLabel->SetLabel(wxString::Format(wxT("%u"), status.lostTriggerSignalsB));

    // 更新数据包统计
    m_statusTotalPacketsLabel->SetLabel(wxString::Format(wxT("%u"), status.totalDataPackets));
    m_statusLostPacketsALabel->SetLabel(wxString::Format(wxT("%u"), status.lostDataPacketsA));
    m_statusLostPacketsBLabel->SetLabel(wxString::Format(wxT("%u"), status.lostDataPacketsB));
    m_statusTotalExecutionsLabel->SetLabel(wxString::Format(wxT("%u"), status.totalValveExecutions));
}
