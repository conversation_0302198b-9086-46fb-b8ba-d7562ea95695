#include "LogFilePackDialog.h"
#include <wx/msgdlg.h>
#include <wx/filename.h>
#include <wx/dir.h>
#include <wx/process.h>
#include <wx/stream.h>
#include <wx/txtstrm.h>
#include <wx/datetime.h>
#include <wx/sizer.h>
#include <wx/statbox.h>
#include <wx/scrolwin.h>

wxBEGIN_EVENT_TABLE(LogFilePackDialog, wxDialog)
    EVT_BUTTON(ID_PACK, LogFilePackDialog::OnPack)
    EVT_BUTTON(wxID_CANCEL, LogFilePackDialog::OnCancel)
    EVT_BUTTON(ID_SELECT_ALL, LogFilePackDialog::OnSelectAll)
    EVT_BUTTON(ID_CLEAR_ALL, LogFilePackDialog::OnClearAll)
wxEND_EVENT_TABLE()

LogFilePackDialog::LogFilePackDialog(wxWindow* parent)
    : wxDialog(parent, wxID_ANY, wxT("日志文件打包"), wxDefaultPosition, wxSize(600, 500),
               wxDEFAULT_DIALOG_STYLE | wxRESIZE_BORDER)
{
    // 初始化日志文件分类
    LogCategory ossCategory(wxT("OSS"));
    ossCategory.files.emplace_back(wxT("nuctech_oss.log"), wxT("/home/<USER>/oss_log/"));
    m_categories.push_back(std::move(ossCategory));
    
    LogCategory mcbCategory(wxT("MCB"));
    mcbCategory.files.emplace_back(wxT("HMCBCtrl.log"), wxT("/home/<USER>/Nuctech_Services/OSS_MR/config/"));
    m_categories.push_back(std::move(mcbCategory));
    
    LogCategory algorithmCategory(wxT("算法"));
    algorithmCategory.files.emplace_back(wxT("ProcLog.log"), wxT("/home/<USER>/Nuctech_Services/OSS_MR/Release/lib/mgspkg/AlgorithmData/"));
    algorithmCategory.files.emplace_back(wxT("RunTime.log"), wxT("/home/<USER>/Nuctech_Services/OSS_MR/Release/lib/mgspkg/AlgorithmData/"));
    m_categories.push_back(std::move(algorithmCategory));
    
    CreateControls();
    CheckFileExistence();
    CenterOnParent();
}

void LogFilePackDialog::CreateControls()
{
    // 创建主布局
    wxBoxSizer* mainSizer = new wxBoxSizer(wxVERTICAL);
    
    // 标题
    m_titleText = new wxStaticText(this, wxID_ANY, wxT("选择需要打包的日志文件"));
    wxFont titleFont = m_titleText->GetFont();
    titleFont.SetPointSize(12);
    titleFont.SetWeight(wxFONTWEIGHT_BOLD);
    m_titleText->SetFont(titleFont);
    mainSizer->Add(m_titleText, 0, wxALL | wxCENTER, 10);
    
    // 创建滚动窗口
    m_scrolledWindow = new wxScrolledWindow(this, wxID_ANY);
    m_scrolledWindow->SetScrollRate(5, 5);
    
    // 文件选择面板
    wxBoxSizer* scrollSizer = new wxBoxSizer(wxVERTICAL);
    
    // 为每个分类创建控件
    for (auto& category : m_categories) {
        // 创建分组框
        wxStaticBoxSizer* groupSizer = new wxStaticBoxSizer(wxVERTICAL, m_scrolledWindow, category.categoryName);
        
        // 为每个文件创建复选框
        for (auto& fileInfo : category.files) {
            wxString label = wxString::Format(wxT("%s"), fileInfo.name);
            wxCheckBox* checkBox = new wxCheckBox(m_scrolledWindow, wxID_ANY, label);
            category.checkBoxes.push_back(checkBox);
            groupSizer->Add(checkBox, 0, wxALL | wxEXPAND, 5);
        }
        
        scrollSizer->Add(groupSizer, 0, wxALL | wxEXPAND, 10);
    }
    
    m_scrolledWindow->SetSizer(scrollSizer);
    mainSizer->Add(m_scrolledWindow, 1, wxALL | wxEXPAND, 10);
    
    // 全选/清空按钮
    wxBoxSizer* selectButtonSizer = new wxBoxSizer(wxHORIZONTAL);
    m_selectAllButton = new wxButton(this, ID_SELECT_ALL, wxT("全选"));
    m_clearAllButton = new wxButton(this, ID_CLEAR_ALL, wxT("清空"));
    
    selectButtonSizer->Add(m_selectAllButton, 0, wxALL, 5);
    selectButtonSizer->Add(m_clearAllButton, 0, wxALL, 5);
    selectButtonSizer->AddStretchSpacer();
    
    mainSizer->Add(selectButtonSizer, 0, wxALL | wxEXPAND, 10);
    
    // 状态文本
    m_statusText = new wxStaticText(this, wxID_ANY, wxT("请选择要打包的日志文件"));
    m_statusText->SetForegroundColour(wxColour(100, 100, 100));
    mainSizer->Add(m_statusText, 0, wxALL | wxCENTER, 5);
    
    // 按钮
    wxBoxSizer* buttonSizer = new wxBoxSizer(wxHORIZONTAL);
    m_packButton = new wxButton(this, ID_PACK, wxT("打包"));
    m_cancelButton = new wxButton(this, wxID_CANCEL, wxT("取消"));
    
    m_packButton->SetDefault();
    
    buttonSizer->AddStretchSpacer();
    buttonSizer->Add(m_cancelButton, 0, wxALL, 5);
    buttonSizer->Add(m_packButton, 0, wxALL, 5);
    
    mainSizer->Add(buttonSizer, 0, wxALL | wxEXPAND, 10);
    
    SetSizer(mainSizer);
}

void LogFilePackDialog::CheckFileExistence()
{
    int totalFiles = 0;
    int existingFiles = 0;
    
    for (auto& category : m_categories) {
        for (size_t i = 0; i < category.files.size(); ++i) {
            auto& fileInfo = category.files[i];
            totalFiles++;
            
            // 检查文件是否存在
            fileInfo.exists = wxFileName::FileExists(fileInfo.fullPath);
            
            if (fileInfo.exists) {
                existingFiles++;
                // 启用复选框
                category.checkBoxes[i]->Enable(true);
            } else {
                // 禁用复选框并标记为不存在
                category.checkBoxes[i]->Enable(false);
                category.checkBoxes[i]->SetForegroundColour(wxColour(150, 150, 150));
                
                wxString label = category.checkBoxes[i]->GetLabel() + wxT(" [文件不存在]");
                category.checkBoxes[i]->SetLabel(label);
            }
        }
    }
    
    // 更新状态文本
    wxString statusMsg = wxString::Format(wxT("找到 %d/%d 个日志文件"), existingFiles, totalFiles);
    m_statusText->SetLabel(statusMsg);
    
    if (existingFiles == 0) {
        m_packButton->Enable(false);
        m_selectAllButton->Enable(false);
    }
}

void LogFilePackDialog::OnPack(wxCommandEvent& WXUNUSED(event))
{
    if (!ValidateSelection()) {
        wxMessageBox(wxT("请至少选择一个日志文件进行打包"), wxT("提示"), 
                     wxOK | wxICON_WARNING, this);
        return;
    }
    
    if (PackSelectedFiles()) {
        wxMessageBox(wxT("日志文件打包完成！"), wxT("成功"), 
                     wxOK | wxICON_INFORMATION, this);
        EndModal(wxID_OK);
    }
}

void LogFilePackDialog::OnCancel(wxCommandEvent& WXUNUSED(event))
{
    EndModal(wxID_CANCEL);
}

void LogFilePackDialog::OnSelectAll(wxCommandEvent& WXUNUSED(event))
{
    for (auto& category : m_categories) {
        for (auto* checkBox : category.checkBoxes) {
            if (checkBox->IsEnabled()) {
                checkBox->SetValue(true);
            }
        }
    }
}

void LogFilePackDialog::OnClearAll(wxCommandEvent& WXUNUSED(event))
{
    for (auto& category : m_categories) {
        for (auto* checkBox : category.checkBoxes) {
            checkBox->SetValue(false);
        }
    }
}

bool LogFilePackDialog::ValidateSelection()
{
    for (auto& category : m_categories) {
        for (auto* checkBox : category.checkBoxes) {
            if (checkBox->IsChecked()) {
                return true;
            }
        }
    }
    return false;
}

bool LogFilePackDialog::PackSelectedFiles()
{
    // 收集选中的文件
    std::vector<wxString> filesToPack;
    
    for (size_t catIdx = 0; catIdx < m_categories.size(); ++catIdx) {
        auto& category = m_categories[catIdx];
        for (size_t fileIdx = 0; fileIdx < category.checkBoxes.size(); ++fileIdx) {
            if (category.checkBoxes[fileIdx]->IsChecked()) {
                filesToPack.push_back(category.files[fileIdx].fullPath);
            }
        }
    }
    
    if (filesToPack.empty()) {
        return false;
    }
    
    // 获取输出路径
    wxString outputPath = GetOutputPath();
    if (outputPath.IsEmpty()) {
        return false;
    }
    
    // 创建压缩包
    return CreateArchive(filesToPack, outputPath);
}

wxString LogFilePackDialog::GetOutputPath()
{
    wxDateTime now = wxDateTime::Now();
    wxString defaultName = wxString::Format(wxT("logs_%s.tar.gz"), 
                                          now.Format(wxT("%Y%m%d_%H%M%S")));
    
    wxFileDialog saveDialog(this, wxT("选择压缩包保存位置"), 
                           wxGetHomeDir(), defaultName,
                           wxT("压缩文件 (*.tar.gz)|*.tar.gz"),
                           wxFD_SAVE | wxFD_OVERWRITE_PROMPT);
    
    if (saveDialog.ShowModal() == wxID_OK) {
        return saveDialog.GetPath();
    }
    
    return wxEmptyString;
}

bool LogFilePackDialog::CreateArchive(const std::vector<wxString>& filesToPack, const wxString& outputPath)
{
    // 显示进度对话框
    wxProgressDialog progressDlg(wxT("打包中"), wxT("正在压缩日志文件..."), 
                                 100, this, 
                                 wxPD_APP_MODAL | wxPD_AUTO_HIDE);
    
    progressDlg.Update(10, wxT("准备文件列表..."));
    
    // 构建tar命令
    wxString command = wxT("tar -czf \"") + outputPath + wxT("\"");
    
    for (const auto& file : filesToPack) {
        command += wxT(" \"") + file + wxT("\"");
    }
    
    progressDlg.Update(30, wxT("执行压缩命令..."));
    
    // 执行压缩命令
    wxArrayString output, errors;
    int result = wxExecute(command, output, errors, wxEXEC_SYNC);
    
    progressDlg.Update(90, wxT("完成压缩..."));
    
    if (result == 0) {
        progressDlg.Update(100, wxT("压缩完成"));
        return true;
    } else {
        wxString errorMsg = wxT("压缩失败");
        if (!errors.IsEmpty()) {
            errorMsg += wxT(":\n") + errors[0];
        }
        wxMessageBox(errorMsg, wxT("错误"), wxOK | wxICON_ERROR, this);
        return false;
    }
}
