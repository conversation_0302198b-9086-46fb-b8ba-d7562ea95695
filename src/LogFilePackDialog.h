#ifndef LOGFILEPACKDIALOG_H
#define LOGFILEPACKDIALOG_H

#include <wx/wx.h>
#include <wx/checkbox.h>
#include <wx/filedlg.h>
#include <wx/dirdlg.h>
#include <wx/progdlg.h>
#include <wx/filename.h>
#include <vector>

struct LogFileInfo {
    wxString name;
    wxString path;
    wxString fullPath;
    bool exists;
    
    LogFileInfo(const wxString& n, const wxString& p) 
        : name(n), path(p), exists(false) {
        fullPath = wxFileName(path, name).GetFullPath();
    }
};

struct LogCategory {
    wxString categoryName;
    std::vector<LogFileInfo> files;
    std::vector<wxCheckBox*> checkBoxes;
    
    LogCategory(const wxString& name) : categoryName(name) {}
};

class LogFilePackDialog : public wxDialog
{
public:
    LogFilePackDialog(wxWindow* parent);

private:
    // Event handlers
    void OnPack(wxCommandEvent& event);
    void OnCancel(wxCommandEvent& event);
    void OnSelectAll(wxCommandEvent& event);
    void OnClearAll(wxCommandEvent& event);
    
    // Helper methods
    void CreateControls();
    void CheckFileExistence();
    bool ValidateSelection();
    bool PackSelectedFiles();
    wxString GetOutputPath();
    bool CreateArchive(const std::vector<wxString>& filesToPack, const wxString& outputPath);
    void ShowResult(bool success, const wxString& message);
    
    // UI components
    wxStaticText* m_titleText;
    wxPanel* m_filePanel;
    wxScrolledWindow* m_scrolledWindow;
    wxButton* m_packButton;
    wxButton* m_cancelButton;
    wxButton* m_selectAllButton;
    wxButton* m_clearAllButton;
    wxStaticText* m_statusText;
    
    // Data
    std::vector<LogCategory> m_categories;
    
    // Button IDs
    enum {
        ID_PACK = 2001,
        ID_SELECT_ALL = 2002,
        ID_CLEAR_ALL = 2003
    };
    
    wxDECLARE_EVENT_TABLE();
};

#endif // LOGFILEPACKDIALOG_H
