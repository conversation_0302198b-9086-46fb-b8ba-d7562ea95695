#include "ChangeConfigDialog.h"
#include <wx/msgdlg.h>
#include <wx/textfile.h>
#include <wx/regex.h>
#include <wx/filename.h>
#include <wx/tokenzr.h>
#include <wx/dir.h>
#include <wx/process.h>
#include <wx/utils.h>
#include <wx/progdlg.h>

#include <cmath>

wxBEGIN_EVENT_TABLE(ChangeConfigDialog, wxDialog)
    EVT_BUTTON(ID_OK_BTN, ChangeConfigDialog::OnOK)
    EVT_BUTTON(ID_CANCEL_BTN, ChangeConfigDialog::OnCancel)
wxEND_EVENT_TABLE()

ChangeConfigDialog::ChangeConfigDialog(wxWindow* parent)
    : wxDialog(parent, wxID_ANY, wxT("修改配置"), wxDefaultPosition, wxSize(1100, 590))
{
    // Create main sizer
    wxBoxSizer* mainSizer = new wxBoxSizer(wxVERTICAL);
    
    // Title
    // wxStaticText* titleText = new wxStaticText(this, wxID_ANY, wxT("配置修改"));
    // wxFont titleFont = titleText->GetFont();
    // titleFont.SetPointSize(14);
    // titleFont.SetWeight(wxFONTWEIGHT_BOLD);
    // titleText->SetFont(titleFont);
    // mainSizer->Add(titleText, 0, wxALL | wxCENTER, 15);
    
    // Configuration panel
    wxStaticBoxSizer* configBox = new wxStaticBoxSizer(wxVERTICAL, this, wxT("配置参数"));
    
    // Detector count
    wxBoxSizer* detectorSizer = new wxBoxSizer(wxHORIZONTAL);
    detectorSizer->Add(new wxStaticText(this, wxID_ANY, wxT("探测器数量:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 10);
    m_detectorCountSpin = new wxSpinCtrl(this, wxID_ANY, wxT("19"), wxDefaultPosition, wxSize(150, 40),
                                        wxSP_ARROW_KEYS, 1, 100, 20);
    detectorSizer->Add(m_detectorCountSpin, 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 20);

    // Four port detector choice
    detectorSizer->Add(new wxStaticText(this, wxID_ANY, wxT("四口探测器:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 10);
    m_fourPortChoice = new wxChoice(this, wxID_ANY, wxDefaultPosition, wxSize(80, 40));
    m_fourPortChoice->Append(wxT("是"));
    m_fourPortChoice->Append(wxT("否"));
    m_fourPortChoice->SetSelection(0); // 默认选择"是"
    detectorSizer->Add(m_fourPortChoice, 0, wxALIGN_CENTER_VERTICAL);

    detectorSizer->AddStretchSpacer(1);
    configBox->Add(detectorSizer, 0, wxALL | wxEXPAND, 15);
    
    // Valve group count
    wxBoxSizer* valveSizer = new wxBoxSizer(wxHORIZONTAL);
    valveSizer->Add(new wxStaticText(this, wxID_ANY, wxT("阀组数量:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 10);
    m_valveGroupCountSpin = new wxSpinCtrl(this, wxID_ANY, wxT("360"), wxDefaultPosition, wxSize(150, 40),
                                          wxSP_ARROW_KEYS, 1, 1000, 90);
    valveSizer->Add(m_valveGroupCountSpin, 0, wxALIGN_CENTER_VERTICAL);
    valveSizer->AddStretchSpacer(1);
    configBox->Add(valveSizer, 0, wxALL | wxEXPAND, 15);

    // Bin file selection
    wxBoxSizer* binFileSizer = new wxBoxSizer(wxHORIZONTAL);
    binFileSizer->Add(new wxStaticText(this, wxID_ANY, wxT("探测器配置文件:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 10);
    m_binFileChoice = new wxChoice(this, wxID_ANY, wxDefaultPosition, wxSize(400, 40));
    binFileSizer->Add(m_binFileChoice, 1, wxALIGN_CENTER_VERTICAL | wxEXPAND);
    configBox->Add(binFileSizer, 0, wxALL | wxEXPAND, 15);

    // Scan for bin files
    ScanBinFiles();
    
    mainSizer->Add(configBox, 0, wxALL | wxEXPAND, 20);
    
    // Description
    wxStaticText* descText = new wxStaticText(this, wxID_ANY,
        wxT("修改后将更新以下文件：\n"
            "• MCBCtrl.ini - MCB0ADNUM、BINFILE参数\n"
            "• oss_launch.launch - DM_NUM、ExtractDMInfoList参数\n"
            "• JetResult.msg - uint8数组大小\n"
            "• CImgDataBuffer.h - PIXELSIZE定义\n"
            "• mgs_flow.cpp - 数组大小定义、g_nDmNum变量\n"
            "• ShareMemDefine.h - JETBYTES定义"));
    descText->SetForegroundColour(wxColour(100, 100, 100));
    mainSizer->Add(descText, 0, wxALL | wxEXPAND, 15);
    
    // Buttons
    wxBoxSizer* buttonSizer = new wxBoxSizer(wxHORIZONTAL);
    m_okButton = new wxButton(this, ID_OK_BTN, wxT("确认修改"));
    m_cancelButton = new wxButton(this, ID_CANCEL_BTN, wxT("取消"));
    
    m_okButton->SetBackgroundColour(wxColour(220, 240, 255));
    m_cancelButton->SetBackgroundColour(wxColour(240, 240, 240));
    
    buttonSizer->Add(m_okButton, 0, wxRIGHT, 10);
    buttonSizer->Add(m_cancelButton, 0);
    
    mainSizer->Add(buttonSizer, 0, wxALL | wxCENTER, 15);
    
    SetSizer(mainSizer);
    SetMinSize(wxSize(1100, 590));
    Centre();
}

void ChangeConfigDialog::OnOK(wxCommandEvent& WXUNUSED(event))
{
    int detectorCount = m_detectorCountSpin->GetValue();
    int valveGroupCount = m_valveGroupCountSpin->GetValue();
    wxString selectedBinFile = m_binFileChoice->GetStringSelection();
    bool isFourPort = (m_fourPortChoice->GetSelection() == 0); // 0="是", 1="否"

    if (selectedBinFile.IsEmpty()) {
        wxMessageBox(wxT("请选择探测器配置文件"), wxT("错误"), wxOK | wxICON_ERROR, this);
        return;
    }

    // 根据四口探测器选择调整探测器数量
    int actualDetectorCount = isFourPort ? (detectorCount + 1) : detectorCount;

    wxString message = wxString::Format(
        wxT("确认要修改配置吗？\n\n")
        wxT("探测器数量: %d\n")
        wxT("四口探测器: %s\n")
        wxT("阀组数量: %d\n")
        wxT("探测器配置文件: %s\n\n")
        wxT("这将修改配置文件，请确保您有足够的权限。"),
        detectorCount, isFourPort ? wxT("是") : wxT("否"), valveGroupCount, selectedBinFile);
    
    int result = wxMessageBox(message, wxT("确认修改"), wxYES_NO | wxICON_QUESTION, this);
    
    if (result == wxYES) {
        bool success = true;
        wxString errorMsg;
        
        // Update MCBCtrl.ini
        if (!UpdateMCBCtrlIni(actualDetectorCount, selectedBinFile)) {
            success = false;
            errorMsg += wxT("• 更新 MCBCtrl.ini 失败\n");
        }

        // Update launch file
        if (!UpdateLaunchFile(actualDetectorCount)) {
            success = false;
            errorMsg += wxT("• 更新 oss_launch.launch 失败\n");
        }
        
        // Update JetResult.msg
        if (!UpdateJetResultMsg(valveGroupCount)) {
            success = false;
            errorMsg += wxT("• 更新 JetResult.msg 失败\n");
        }

        // Update CImgDataBuffer.h
        if (!UpdateCImgDataBufferH(detectorCount)) {
            success = false;
            errorMsg += wxT("• 更新 CImgDataBuffer.h 失败\n");
        }

        // Update mgs_flow.cpp
        if (!UpdateMgsFlowCpp(detectorCount)) {
            success = false;
            errorMsg += wxT("• 更新 mgs_flow.cpp 失败\n");
        }

        // Update ShareMemDefine.h
        if (!UpdateShareMemDefineH(valveGroupCount)) {
            success = false;
            errorMsg += wxT("• 更新 ShareMemDefine.h 失败\n");
        }
        
        if (success) {
            wxMessageBox(wxT("配置文件更新成功！\n\n正在启动编译..."), wxT("成功"), wxOK | wxICON_INFORMATION, this);

            // 启动编译过程
            if (StartCompilation()) {
                EndModal(wxID_OK);
            } else {
                // 即使编译失败，配置文件已经更新成功，仍然关闭对话框
                wxMessageBox(wxT("配置文件更新成功，但编译启动失败。\n请手动执行编译命令。"),
                            wxT("警告"), wxOK | wxICON_WARNING, this);
                EndModal(wxID_OK);
            }
        } else {
            wxMessageBox(wxT("部分配置文件更新失败：\n\n") + errorMsg + 
                        wxT("\n请检查文件权限和路径是否正确。"), 
                        wxT("错误"), wxOK | wxICON_ERROR, this);
        }
    }
}

void ChangeConfigDialog::OnCancel(wxCommandEvent& WXUNUSED(event))
{
    EndModal(wxID_CANCEL);
}

bool ChangeConfigDialog::UpdateMCBCtrlIni(int detectorCount, const wxString& binFileName)
{
    wxString filePath = wxT("/home/<USER>/Nuctech_Services/OSS_MR/config/MCBCtrl.ini");
    wxString content;

    // Check if file exists
    if (!wxFileName::FileExists(filePath)) {
        wxMessageBox(wxString::Format(wxT("文件不存在: %s"), filePath), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    // Check if file is readable
    if (!wxFileName::IsFileReadable(filePath)) {
        wxMessageBox(wxString::Format(wxT("文件不可读: %s"), filePath), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    // Check if file is writable
    if (!wxFileName::IsFileWritable(filePath)) {
        wxMessageBox(wxString::Format(wxT("文件不可写: %s\n请检查文件权限"), filePath), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    if (!ReadFileContent(filePath, content)) {
        wxMessageBox(wxT("读取MCBCtrl.ini文件失败"), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    bool mcbAdnumUpdated = false;
    bool binFileUpdated = false;

    // Use regex to find and replace MCB0ADNUM (format: MCB0ADNUM =20)
    wxRegEx mcbRegex(wxT("MCB0ADNUM[ \\t]*=[ \\t]*[0-9]+"));
    if (mcbRegex.IsValid() && mcbRegex.Matches(content)) {
        wxString replacement = wxString::Format(wxT("MCB0ADNUM =%d"), detectorCount);
        mcbRegex.Replace(&content, replacement);
        mcbAdnumUpdated = true;
    }

    // Find BINFILE line and replace it directly in content
    wxArrayString lines = wxSplit(content, '\n');

    for (size_t i = 0; i < lines.GetCount(); i++) {
        if (lines[i].Contains(wxT("BINFILE"))) {
            // Replace the entire line with new BINFILE value
            lines[i] = wxString::Format(wxT("BINFILE = %s"), binFileName);
            binFileUpdated = true;
            break;
        }
    }

    // Reconstruct content from modified lines
    if (binFileUpdated) {
        content = wxJoin(lines, '\n');
    }

    // Check if at least one parameter was updated
    if (mcbAdnumUpdated || binFileUpdated) {
        if (!WriteFileContent(filePath, content)) {
            wxMessageBox(wxT("写入MCBCtrl.ini文件失败"), wxT("错误"), wxOK | wxICON_ERROR);
            return false;
        }
        return true;
    } else {
        wxString errorMsg = wxT("在MCBCtrl.ini中未找到以下参数：\n");
        if (!mcbAdnumUpdated) errorMsg += wxT("• MCB0ADNUM\n");
        if (!binFileUpdated) errorMsg += wxT("• BINFILE\n");
        wxMessageBox(errorMsg, wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }
}

bool ChangeConfigDialog::UpdateLaunchFile(int detectorCount)
{
    wxString filePath = wxT("/home/<USER>/Nuctech_Services/OSS_MR/src/oss_launcher/launch/oss_launch.launch");
    wxString content;

    // Check if file exists
    if (!wxFileName::FileExists(filePath)) {
        wxMessageBox(wxString::Format(wxT("文件不存在: %s"), filePath), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    // Check if file is writable
    if (!wxFileName::IsFileWritable(filePath)) {
        wxMessageBox(wxString::Format(wxT("文件不可写: %s\n请检查文件权限"), filePath), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    if (!ReadFileContent(filePath, content)) {
        wxMessageBox(wxT("读取oss_launch.launch文件失败"), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    bool dmNumUpdated = false;
    bool extractDMInfoUpdated = false;

    // Use regex to find and replace DM_NUM default value
    wxRegEx dmNumRegex(wxT("<arg[ \\t]+name=\"DM_NUM\"[^>]*"));
    if (dmNumRegex.IsValid() && dmNumRegex.Matches(content)) {
        wxString replacement = wxString::Format(wxT("<arg name=\"DM_NUM\" \t \t\tdefault=\"%d\"/"), detectorCount);
        dmNumRegex.Replace(&content, replacement);
        dmNumUpdated = true;
    }

    // Use regex to find and replace ExtractDMInfoList value
    wxRegEx extractRegex(wxT("<arg[ \\t]+name=\"ExtractDMInfoList\"[^>]*"));
    if (extractRegex.IsValid() && extractRegex.Matches(content)) {
        wxString replacement = wxString::Format(wxT("<arg name=\"ExtractDMInfoList\"           value=\"0:0:%d;\"/"), detectorCount);
        extractRegex.Replace(&content, replacement);
        extractDMInfoUpdated = true;
    }

    // Check if at least one parameter was updated
    if (dmNumUpdated || extractDMInfoUpdated) {
        if (!WriteFileContent(filePath, content)) {
            wxMessageBox(wxT("写入oss_launch.launch文件失败"), wxT("错误"), wxOK | wxICON_ERROR);
            return false;
        }
        return true;
    } else {
        wxString errorMsg = wxT("在oss_launch.launch中未找到以下参数：\n");
        if (!dmNumUpdated) errorMsg += wxT("• DM_NUM\n");
        if (!extractDMInfoUpdated) errorMsg += wxT("• ExtractDMInfoList\n");
        wxMessageBox(errorMsg, wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }
}

bool ChangeConfigDialog::UpdateJetResultMsg(int valveGroupCount)
{
    wxString filePath = wxT("/home/<USER>/Nuctech_Services/OSS_MR/src/mgsPkg/msg/JetResult.msg");
    wxString content;

    // Check if file exists
    if (!wxFileName::FileExists(filePath)) {
        wxMessageBox(wxString::Format(wxT("文件不存在: %s"), filePath), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    // Check if file is writable
    if (!wxFileName::IsFileWritable(filePath)) {
        wxMessageBox(wxString::Format(wxT("文件不可写: %s\n请检查文件权限"), filePath), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    if (!ReadFileContent(filePath, content)) {
        wxMessageBox(wxT("读取JetResult.msg文件失败"), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    int arraySize = CalculateArraySize(valveGroupCount);

    // Use regex to find and replace uint8[number]
    wxRegEx regex(wxT("uint8\\[[0-9]+\\]"));
    if (regex.IsValid() && regex.Matches(content)) {
        wxString replacement = wxString::Format(wxT("uint8[%d]"), arraySize);
        regex.Replace(&content, replacement);

        if (!WriteFileContent(filePath, content)) {
            wxMessageBox(wxT("写入JetResult.msg文件失败"), wxT("错误"), wxOK | wxICON_ERROR);
            return false;
        }
        return true;
    } else {
        wxMessageBox(wxT("在JetResult.msg中未找到uint8数组定义"), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }
}

bool ChangeConfigDialog::ReadFileContent(const wxString& filePath, wxString& content)
{
    // 使用wxFile以二进制模式读取，保留所有格式
    wxFile file;
    if (!file.Open(filePath, wxFile::read)) {
        return false;
    }
    
    // 获取文件大小
    wxFileOffset length = file.Length();
    if (length == wxInvalidOffset) {
        file.Close();
        return false;
    }
    
    // 读取整个文件内容
    char* buffer = new char[length + 1];
    size_t bytesRead = file.Read(buffer, length);
    buffer[bytesRead] = '\0';  // 确保字符串结束
    
    // 转换为wxString
    content = wxString::FromUTF8(buffer);
    if (content.IsEmpty()) {
        // 如果UTF-8转换失败，尝试本地编码
        content = wxString(buffer, wxConvLocal);
    }
    
    // 清理并关闭
    delete[] buffer;
    file.Close();
    return true;
}

bool ChangeConfigDialog::WriteFileContent(const wxString& filePath, const wxString& content)
{
    // 创建备份
    // wxString backupPath = filePath + wxT(".backup");
    // if (wxFileName::FileExists(filePath)) {
    //     wxCopyFile(filePath, backupPath);
    // }
    
    // 使用wxFile以二进制模式写入，保留所有格式
    wxFile file;
    if (!file.Open(filePath, wxFile::write)) {
        return false;
    }
    
    // 将wxString转换为UTF-8并写入
    wxScopedCharBuffer buffer = content.ToUTF8();
    bool success = (file.Write(buffer.data(), buffer.length()) == buffer.length());
    
    file.Close();
    return success;
}

int ChangeConfigDialog::CalculateArraySize(int valveGroupCount)
{
    // Calculate valveGroupCount * 125 / 8, rounded up
    // double result = (double)(valveGroupCount * 125) / 8.0;
    double result = (double)(valveGroupCount / 8.0);  
    int num =  125 * (int)std::ceil(result);
    return num;
}

bool ChangeConfigDialog::UpdateCImgDataBufferH(int detectorCount)
{
    wxString filePath = wxT("/home/<USER>/Nuctech_Services/OSS_MR/src/mgsPkg/src/CImgDataBuffer.h");
    wxString content;

    // Check if file exists
    if (!wxFileName::FileExists(filePath)) {
        wxMessageBox(wxString::Format(wxT("文件不存在: %s"), filePath), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    // Check if file is writable
    if (!wxFileName::IsFileWritable(filePath)) {
        wxMessageBox(wxString::Format(wxT("文件不可写: %s\n请检查文件权限"), filePath), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    if (!ReadFileContent(filePath, content)) {
        wxMessageBox(wxT("读取CImgDataBuffer.h文件失败"), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    // Calculate new PIXELSIZE value (detectorCount * 64)
    int newPixelSize = detectorCount  * 64;

    // Use regex to find and replace PIXELSIZE (format: #define PIXELSIZE 1280)
    wxRegEx regex(wxT("#define[ \\t]+PIXELSIZE[ \\t]+[0-9]+"));
    if (regex.IsValid() && regex.Matches(content)) {
        wxString replacement = wxString::Format(wxT("#define PIXELSIZE %d"), newPixelSize);
        regex.Replace(&content, replacement);

        if (!WriteFileContent(filePath, content)) {
            wxMessageBox(wxT("写入CImgDataBuffer.h文件失败"), wxT("错误"), wxOK | wxICON_ERROR);
            return false;
        }
        return true;
    } else {
        wxMessageBox(wxT("在CImgDataBuffer.h中未找到PIXELSIZE定义"), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }
}

bool ChangeConfigDialog::UpdateMgsFlowCpp(int detectorCount)
{
    wxString filePath = wxT("/home/<USER>/Nuctech_Services/OSS_MR/src/mgsPkg/src/mgs_flow.cpp");
    wxString content;

    // Check if file exists
    if (!wxFileName::FileExists(filePath)) {
        wxMessageBox(wxString::Format(wxT("文件不存在: %s"), filePath), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    // Check if file is writable
    if (!wxFileName::IsFileWritable(filePath)) {
        wxMessageBox(wxString::Format(wxT("文件不可写: %s\n请检查文件权限"), filePath), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    if (!ReadFileContent(filePath, content)) {
        wxMessageBox(wxT("读取mgs_flow.cpp文件失败"), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    // Calculate new array size (detectorCount * 64)
    int newArraySize = detectorCount * 64;

    // Calculate new g_nDmNum value (detectorCount - 1)
    int newDmNum = detectorCount ;

    bool line127Updated = false;
    bool line128Updated = false;
    bool gDmNumUpdated = false;

    // Use regex to find and replace line 127: unsigned char highData[1280* 2 * 250];
    wxRegEx regex127(wxT("unsigned char highData\\[[^;]*"));
    if (regex127.IsValid() && regex127.Matches(content)) {
        wxString replacement = wxString::Format(wxT("unsigned char highData[%d* 2 * 250]"), newArraySize);
        regex127.Replace(&content, replacement);
        line127Updated = true;
    }

    // Use regex to find and replace line 128: unsigned char lowData[1280 * 2 * 250];
    wxRegEx regex128(wxT("unsigned char lowData\\[[^;]*"));
    if (regex128.IsValid() && regex128.Matches(content)) {
        wxString replacement = wxString::Format(wxT("unsigned char lowData[%d * 2 * 250]"), newArraySize);
        regex128.Replace(&content, replacement);
        line128Updated = true;
    }

    // Use regex to find and replace g_nDmNum: int g_nDmNum = 19;
    wxRegEx regexDmNum(wxT("int[ \\t]+g_nDmNum[ \\t]*=[ \\t]*[0-9]+[ \\t]*;"));
    if (regexDmNum.IsValid() && regexDmNum.Matches(content)) {
        wxString replacement = wxString::Format(wxT("int g_nDmNum = %d;"), newDmNum);
        regexDmNum.Replace(&content, replacement);
        gDmNumUpdated = true;
    }

    // Check if at least one parameter was updated
    if (line127Updated || line128Updated || gDmNumUpdated) {
        if (!WriteFileContent(filePath, content)) {
            wxMessageBox(wxT("写入mgs_flow.cpp文件失败"), wxT("错误"), wxOK | wxICON_ERROR);
            return false;
        }
        return true;
    } else {
        wxString errorMsg = wxT("在mgs_flow.cpp中未找到以下定义：\n");
        if (!line127Updated) errorMsg += wxT("• highData数组\n");
        if (!line128Updated) errorMsg += wxT("• lowData数组\n");
        if (!gDmNumUpdated) errorMsg += wxT("• g_nDmNum变量\n");
        wxMessageBox(errorMsg, wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }
}

bool ChangeConfigDialog::UpdateShareMemDefineH(int valveGroupCount)
{
    wxString filePath = wxT("/home/<USER>/Nuctech_Services/OSS_MR/src/mgsPkg/src/ShareMemDefine.h");
    wxString content;

    // Check if file exists
    if (!wxFileName::FileExists(filePath)) {
        wxMessageBox(wxString::Format(wxT("文件不存在: %s"), filePath), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    // Check if file is writable
    if (!wxFileName::IsFileWritable(filePath)) {
        wxMessageBox(wxString::Format(wxT("文件不可写: %s\n请检查文件权限"), filePath), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    if (!ReadFileContent(filePath, content)) {
        wxMessageBox(wxT("读取ShareMemDefine.h文件失败"), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }

    // Calculate new JETBYTES value (valveGroupCount * 125 / 8, rounded up)
    int newJetBytes = CalculateArraySize(valveGroupCount);

    // Use regex to find and replace JETBYTES (format: #define JETBYTES 5625)
    wxRegEx regex(wxT("#define[ \\t]+JETBYTES[ \\t]+[0-9]+"));
    if (regex.IsValid() && regex.Matches(content)) {
        wxString replacement = wxString::Format(wxT("#define JETBYTES %d"), newJetBytes);
        regex.Replace(&content, replacement);

        if (!WriteFileContent(filePath, content)) {
            wxMessageBox(wxT("写入ShareMemDefine.h文件失败"), wxT("错误"), wxOK | wxICON_ERROR);
            return false;
        }
        return true;
    } else {
        wxMessageBox(wxT("在ShareMemDefine.h中未找到JETBYTES定义"), wxT("错误"), wxOK | wxICON_ERROR);
        return false;
    }
}

void ChangeConfigDialog::ScanBinFiles()
{
    wxString configPath = wxT("/home/<USER>/Nuctech_Services/OSS_MR/config/");

    // Clear existing items
    m_binFileChoice->Clear();

    // Check if directory exists
    if (!wxDir::Exists(configPath)) {
        m_binFileChoice->Append(wxT("配置目录不存在"));
        m_binFileChoice->SetSelection(0);
        m_binFileChoice->Enable(false);
        return;
    }

    // Scan for .bin files
    wxDir dir(configPath);
    if (!dir.IsOpened()) {
        m_binFileChoice->Append(wxT("无法打开配置目录"));
        m_binFileChoice->SetSelection(0);
        m_binFileChoice->Enable(false);
        return;
    }

    wxString filename;
    wxArrayString binFiles;

    // Get all .bin files
    bool cont = dir.GetFirst(&filename, wxT("*.bin"), wxDIR_FILES);
    while (cont) {
        binFiles.Add(filename);
        cont = dir.GetNext(&filename);
    }

    // Sort the files alphabetically
    binFiles.Sort();

    // Add files to choice control
    for (size_t i = 0; i < binFiles.GetCount(); i++) {
        m_binFileChoice->Append(binFiles[i]);
    }

    // Try to select current BINFILE from MCBCtrl.ini
    wxString currentBinFile = GetCurrentBinFile();
    if (!currentBinFile.IsEmpty()) {
        int index = m_binFileChoice->FindString(currentBinFile);
        if (index != wxNOT_FOUND) {
            m_binFileChoice->SetSelection(index);
        } else if (m_binFileChoice->GetCount() > 0) {
            // If current file not found, select first one
            m_binFileChoice->SetSelection(0);
        }
    } else if (m_binFileChoice->GetCount() > 0) {
        // If no current file found, select first one
        m_binFileChoice->SetSelection(0);
    }

    if (m_binFileChoice->GetCount() == 0) {
        m_binFileChoice->Append(wxT("未找到.bin文件"));
        m_binFileChoice->SetSelection(0);
        m_binFileChoice->Enable(false);
    }
}

wxString ChangeConfigDialog::GetCurrentBinFile()
{
    wxString filePath = wxT("/home/<USER>/Nuctech_Services/OSS_MR/config/MCBCtrl.ini");
    wxString content;

    // Try to read current BINFILE from MCBCtrl.ini
    if (!ReadFileContent(filePath, content)) {
        return wxEmptyString;
    }

    // Use regex to find BINFILE line and capture the filename
    wxRegEx regex(wxT("BINFILE[ \\t]*=[ \\t]*([^\\r\\n\\t ]+)"));
    if (regex.IsValid() && regex.Matches(content)) {
        // Get the captured group (filename) and trim whitespace
        wxString fileName = regex.GetMatch(content, 1);
        fileName.Trim(true).Trim(false); // Remove leading and trailing whitespace

        // Fix common data error: if filename ends with .binn, correct it to .bin
        if (fileName.EndsWith(wxT(".binn"))) {
            fileName = fileName.Left(fileName.Length() - 1); // Remove the extra 'n'
        }

        return fileName;
    }

    return wxEmptyString;
}

bool ChangeConfigDialog::StartCompilation()
{
    // 检测是否在AppImage环境中运行
    wxString appImagePath, appDir;
    bool isAppImage = wxGetEnv(wxT("APPIMAGE"), &appImagePath) || wxGetEnv(wxT("APPDIR"), &appDir);

    wxString workingDir;
    wxString scriptPath;
    wxString scriptArgs = wxT("mgspkg -r 1");

    if (isAppImage) {
        // AppImage环境：使用打包的脚本
        if (wxGetEnv(wxT("APPDIR"), &appDir)) {
            // 在AppImage内部，脚本应该在usr/bin目录下
            scriptPath = appDir + wxT("/usr/bin/single_pkg_build.sh");
            workingDir = wxT("/home/<USER>/Nuctech_Services/OSS_MR");
        } else {
            // 如果无法获取APPDIR，尝试相对路径
            scriptPath = wxT("./single_pkg_build.sh");
            workingDir = wxT("/home/<USER>/Nuctech_Services/OSS_MR");
        }
    } else {
        // 本地环境：使用系统路径
        workingDir = wxT("/home/<USER>/Nuctech_Services/OSS_MR");
        scriptPath = wxT("/home/<USER>/Nuctech_Services/OSS_MR/single_pkg_build.sh");
    }

    // 检查工作目录是否存在
    if (!wxDir::Exists(workingDir)) {
        wxMessageBox(wxString::Format(wxT("编译目录不存在: %s"), workingDir),
                    wxT("错误"), wxOK | wxICON_ERROR, this);
        return false;
    }

    // 检查编译脚本是否存在
    if (!wxFileName::FileExists(scriptPath)) {
        wxMessageBox(wxString::Format(wxT("编译脚本不存在: %s\n\n请确保脚本已正确打包到AppImage中。"), scriptPath),
                    wxT("错误"), wxOK | wxICON_ERROR, this);
        return false;
    }

    // 显示编译开始消息
    wxMessageBox(wxT("正在启动编译，请稍候..."), wxT("编译中"), wxOK | wxICON_INFORMATION, this);

    // 设置脚本执行权限
    wxString chmodCommand = wxString::Format(wxT("chmod +x \"%s\""), scriptPath);
    wxExecute(chmodCommand, wxEXEC_SYNC);

    // 显示进度对话框
    wxProgressDialog progressDlg(wxT("编译进度"),
                                 wxT("正在启动编译，请稍候..."),
                                 100, this,
                                 wxPD_APP_MODAL | wxPD_CAN_ABORT | wxPD_ELAPSED_TIME);

    // 平滑的进度更新
    for (int i = 0; i <= 10; i++) {
        progressDlg.Update(i, wxT("正在检查编译环境..."));
        wxMilliSleep(50);
        wxYield(); // 让界面响应
    }

    for (int i = 11; i <= 25; i++) {
        progressDlg.Update(i, wxT("正在执行编译脚本..."));
        wxMilliSleep(30);
        wxYield();
    }

    // 构建编译命令，先加载环境变量
    wxString setupScript = wxT("/home/<USER>/Nuctech_Services/OSS_MR/Release/setup.bash");
    wxString bashCommand;

    if (isAppImage) {
        // AppImage环境：先加载环境变量，然后切换到工作目录，执行打包的脚本
        bashCommand = wxString::Format(
            wxT("bash -c \"source %s && cd %s && bash '%s' %s\""),
            setupScript, workingDir, scriptPath, scriptArgs);
    } else {
        // 本地环境：先加载环境变量，然后执行脚本
        bashCommand = wxString::Format(
            wxT("bash -c \"source %s && cd %s && bash %s %s\""),
            setupScript, workingDir, scriptPath, scriptArgs);
    }
    wxArrayString output, errors;

    // 编译阶段的进度更新 - 使用脉冲模式显示持续活动
    // int currentProgress = 30;

    // 在编译开始前显示进度
    for (int i = 30; i <= 35; i++) {
        progressDlg.Update(i, wxT("正在编译源代码..."));
        wxMilliSleep(100);
        wxYield();
    }

    // 执行编译命令
    int exitCode = wxExecute(bashCommand, output, errors, wxEXEC_SYNC);

    // 编译完成后快速更新到80%
    for (int i = 36; i <= 80; i += 4) {
        progressDlg.Update(i, wxT("编译完成，正在处理..."));
        wxMilliSleep(20);
        wxYield();
    }

    // 编译完成后的进度更新
    for (int i = 85; i <= 95; i++) {
        progressDlg.Update(i, wxT("编译完成，正在处理结果..."));
        wxMilliSleep(40);
        wxYield();
    }

    progressDlg.Update(100, wxT("编译任务完成"));
    wxMilliSleep(500);
    wxYield();

    // 检查编译结果
    if (exitCode == 0) {
        // 编译成功
        wxMessageBox(wxT("编译完成！请重启OSS服务"), wxT("编译成功"), wxOK | wxICON_INFORMATION, this);
        return true;
    } else {
        // 编译失败
        wxString errorMsg = wxString::Format(wxT("编译失败！\n\n退出代码: %d\n执行命令: %s\n\n"), exitCode, bashCommand);

        if (!errors.IsEmpty()) {
            errorMsg += wxT("错误信息：\n");
            for (size_t i = 0; i < errors.GetCount() && i < 5; i++) {
                errorMsg += errors[i] + wxT("\n");
            }
            if (errors.GetCount() > 5) {
                errorMsg += wxT("...(更多错误信息)\n");
            }
        }

        if (!output.IsEmpty()) {
            errorMsg += wxT("\n输出信息：\n");
            for (size_t i = 0; i < output.GetCount() && i < 3; i++) {
                errorMsg += output[i] + wxT("\n");
            }
            if (output.GetCount() > 3) {
                errorMsg += wxT("...(更多输出信息)\n");
            }
        }

        errorMsg += wxT("\n请检查编译环境和依赖项。\n");
        errorMsg += wxT("您也可以手动执行编译命令进行调试。");

        wxMessageBox(errorMsg, wxT("编译失败"), wxOK | wxICON_ERROR, this);
        return false;
    }
}
