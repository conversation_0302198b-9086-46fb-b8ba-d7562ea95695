#include "DeleteSourceDialog.h"
#include <wx/filename.h>
#include <wx/dir.h>
#include <wx/datetime.h>
#include <wx/process.h>
#include <wx/stream.h>
#include <wx/txtstrm.h>
#include <wx/artprov.h>
#include <wx/stdpaths.h>
#include <wx/progdlg.h>

const wxString DeleteSourceDialog::OSS_MR_PATH = wxT(""); // Will be determined at runtime

wxBEGIN_EVENT_TABLE(DeleteSourceDialog, wxDialog)
    EVT_BUTTON(wxID_OK, DeleteSourceDialog::OnConfirm)
    EVT_BUTTON(wxID_CANCEL, DeleteSourceDialog::OnCancel)
wxEND_EVENT_TABLE()

wxString DeleteSourceDialog::FindOSSMRPath()
{
    // Try to find OSS_MR directory in common locations
    wxArrayString possiblePaths;

    // Add common locations
    possiblePaths.Add(wxT("/home/<USER>/Nuctech_Services/OSS_MR"));     // Original path
    possiblePaths.Add(wxT("./Nuctech_Services/OSS_MR"));                 // Relative to current dir
    possiblePaths.Add(wxT("../Nuctech_Services/OSS_MR"));                // Parent directory
    possiblePaths.Add(wxT("/opt/Nuctech_Services/OSS_MR"));              // System installation
    possiblePaths.Add(wxT("/usr/local/Nuctech_Services/OSS_MR"));        // Local installation

    // Get user's home directory
    wxString homeDir = wxStandardPaths::Get().GetUserDataDir();
    homeDir = homeDir.BeforeFirst(wxT('.'));  // Remove .appname part
    possiblePaths.Add(homeDir + wxT("/Nuctech_Services/OSS_MR"));

    // Check each possible path
    for (size_t i = 0; i < possiblePaths.GetCount(); i++) {
        if (wxDir::Exists(possiblePaths[i])) {
            // Verify it looks like the right directory by checking for src subdirectory
            wxString srcPath = possiblePaths[i] + wxT("/src");
            if (wxDir::Exists(srcPath)) {
                return possiblePaths[i];
            }
        }
    }

    // If not found, return empty string
    return wxEmptyString;
}

DeleteSourceDialog::DeleteSourceDialog(wxWindow* parent)
    : wxDialog(parent, wxID_ANY, wxT("删除源码确认"), wxDefaultPosition, wxSize(600, 450))
{
    // Find OSS_MR path dynamically
    wxString ossMRPath = FindOSSMRPath();
    if (ossMRPath.IsEmpty()) {
        // Show warning if path not found
        wxMessageBox(wxT("警告：未找到 OSS_MR 目录！\n请确保目录存在或手动指定路径。"),
                     wxT("路径警告"), wxOK | wxICON_WARNING, this);
    }
    // Create main sizer
    wxBoxSizer* mainSizer = new wxBoxSizer(wxVERTICAL);
    
    // Warning icon and text
    wxBoxSizer* warningBox = new wxBoxSizer(wxHORIZONTAL);
    
    // Add warning icon (using system icon)
    wxStaticBitmap* warningIcon = new wxStaticBitmap(this, wxID_ANY, 
        wxArtProvider::GetBitmap(wxART_WARNING, wxART_MESSAGE_BOX, wxSize(32, 32)));
    warningBox->Add(warningIcon, 0, wxALL | wxALIGN_CENTER_VERTICAL, 10);
    
    // Warning text
    m_warningText = new wxStaticText(this, wxID_ANY, 
        wxT("警告：此操作将删除源码文件！请先停止OSS服务！"));
    wxFont warningFont = m_warningText->GetFont();
    warningFont.SetPointSize(14);
    warningFont.SetWeight(wxFONTWEIGHT_BOLD);
    m_warningText->SetFont(warningFont);
    m_warningText->SetForegroundColour(wxColour(200, 0, 0));
    warningBox->Add(m_warningText, 1, wxALL | wxALIGN_CENTER_VERTICAL, 10);
    
    mainSizer->Add(warningBox, 0, wxEXPAND);
    
    // Detail text
    m_detailText = new wxStaticText(this, wxID_ANY,
        wxT("此操作将执行以下步骤：\n\n")
        wxT("1. 选择是否备份 OSS_MR 文件夹\n")
        wxT("2. 删除以下目录中的所有 .cpp 文件：\n")
        wxT("   • mgsPkg\n")
        wxT("   • http_module\n")
        wxT("   • mcb_parser\n")
        wxT("   • mcb_reader\n")
        wxT("   • mcb_recorder\n")
        wxT("   • process_collection\n\n")
        wxT("请确认是否继续？"));

    // Enable text wrapping
    m_detailText->Wrap(650);

    mainSizer->Add(m_detailText, 1, wxALL | wxEXPAND, 20);
    
    // Button sizer
    wxBoxSizer* buttonSizer = new wxBoxSizer(wxHORIZONTAL);
    
    m_confirmButton = new wxButton(this, wxID_OK, wxT("确认"));
    m_confirmButton->SetBackgroundColour(wxColour(255, 200, 200));
    
    m_cancelButton = new wxButton(this, wxID_CANCEL, wxT("取消"));
    m_cancelButton->SetBackgroundColour(wxColour(200, 255, 200));
    
    buttonSizer->Add(m_cancelButton, 0, wxALL, 5);
    buttonSizer->AddStretchSpacer();
    buttonSizer->Add(m_confirmButton, 0, wxALL, 5);
    
    mainSizer->Add(buttonSizer, 0, wxALL | wxEXPAND, 20);
    
    SetSizer(mainSizer);
    Centre();
    
    // Set focus to cancel button for safety
    m_cancelButton->SetFocus();
}

void DeleteSourceDialog::OnConfirm(wxCommandEvent& WXUNUSED(event))
{
    // First ask if user wants to backup before deletion
    int preBackupChoice = wxMessageBox(
        wxT("是否需要在删除源码前备份 OSS_MR 文件夹？\n\n")
        wxT("选择\"是\"：先备份再删除源码\n")
        wxT("选择\"否\"：直接删除源码，不进行备份\n")
        wxT("选择\"取消\"：取消整个操作"),
        wxT("删除前备份确认"),
        wxYES_NO | wxCANCEL | wxICON_QUESTION,
        this
    );

    if (preBackupChoice == wxCANCEL) {
        return; // User cancelled the operation
    }

    bool preBackupDone = false;

    // Backup before deletion if requested
    if (preBackupChoice == wxYES) {
        preBackupDone = BackupOSSMR();
        if (!preBackupDone) {
            return; // Error already shown
        }
    }

    if (DeleteCppFiles()) {
        // if (!preBackupDone) {
            int postBackupChoice = wxMessageBox(
                wxT("源码删除完成！\n\n是否需要备份 OSS_MR 文件夹？\n\n")
                wxT("选择\"是\"：创建备份文件\n")
                wxT("选择\"否\"：直接结束"),
                wxT("删除后备份确认"),
                wxYES_NO | wxICON_QUESTION,
                this
            );

            if (postBackupChoice == wxYES) {
                if (BackupOSSMR()) {
                    ShowResult(true, wxT("操作完成！\n\n源码已删除，备份文件已保存。"));
                } else {
                    ShowResult(false, wxT("源码删除完成，但备份失败！"));
                }
            } else {
                ShowResult(true, wxT("操作完成！\n\n源码已删除。"));
            }
        // } else {
        //     // Pre-backup was done, show completion message
        //     ShowResult(true, wxT("操作完成！\n\n备份文件已保存，源码已删除。"));
        // }
    }

    EndModal(wxID_OK);
}

void DeleteSourceDialog::OnCancel(wxCommandEvent& WXUNUSED(event))
{
    EndModal(wxID_CANCEL);
}

bool DeleteSourceDialog::BackupOSSMR()
{
    // Find OSS_MR path dynamically
    wxString ossMRPath = FindOSSMRPath();
    if (ossMRPath.IsEmpty()) {
        wxMessageBox(wxT("错误：未找到 OSS_MR 目录！\n请确保目录存在。"),
                     wxT("错误"), wxOK | wxICON_ERROR, this);
        return false;
    }

    // Check if source directory exists
    if (!wxDir::Exists(ossMRPath)) {
        wxMessageBox(wxT("错误：源目录不存在！\n") + ossMRPath,
                     wxT("错误"), wxOK | wxICON_ERROR, this);
        return false;
    }

    // Generate backup filename with timestamp
    wxDateTime now = wxDateTime::Now();
    wxString timestamp = now.Format(wxT("%Y%m%d_%H%M%S"));

    // Use /tmp directory for backup to avoid permission issues
    wxString backupPath = wxString::Format(wxT("/home/<USER>/Nuctech_Services/OSS_MR_backup_%s.tar.gz"), timestamp);
    
    // Create progress dialog for compression
    wxProgressDialog* progressDialog = new wxProgressDialog(
        wxT("正在备份"),
        wxT("正在压缩文件，请稍候..."),
        100,  // range
        this,
        wxPD_APP_MODAL | wxPD_AUTO_HIDE | wxPD_CAN_ABORT | wxPD_ESTIMATED_TIME
    );

    // 确保路径中没有特殊字符，使用引号包围路径
    wxString safeSourcePath = ossMRPath;
    wxString safeBackupPath = backupPath;
    
    // 使用tar命令压缩，确保命令格式正确
    wxString command = wxT("tar -czf \"") + safeBackupPath + wxT("\" -C \"") + safeSourcePath + wxT("\" .");
    
    // 打印命令用于调试
    // wxLogMessage(wxT("执行命令: %s"), command);

    // Execute tar command asynchronously
    long pid = wxExecute(command, wxEXEC_ASYNC);

    if (pid == 0) {
        delete progressDialog;
        wxMessageBox(wxT("无法启动备份进程！"),
                     wxT("错误"), wxOK | wxICON_ERROR, this);
        return false;
    }

    // Monitor progress by checking file size growth
    wxULongLong lastSize = 0;
    int progress = 0;
    bool cancelled = false;
    int stableCount = 0;  // Count how many times size hasn't changed

    while (true) {
        // Check if process is still running
        if (!wxProcess::Exists(pid)) {
            progress = 100;
            progressDialog->Update(progress, wxT("压缩完成"));
            wxMilliSleep(500); // Show completion briefly
            break;
        }

        // Update progress based on backup file size growth
        if (wxFileName::FileExists(backupPath)) {
            wxULongLong currentSize = wxFileName::GetSize(backupPath);
            if (currentSize > lastSize) {
                lastSize = currentSize;
                progress = wxMin(95, progress + 3); // Gradually increase progress
                stableCount = 0;

                wxString sizeStr;
                if (currentSize > 1024 * 1024) {
                    sizeStr = wxString::Format(wxT("%.1f MB"), currentSize.ToDouble() / (1024 * 1024));
                } else if (currentSize > 1024) {
                    sizeStr = wxString::Format(wxT("%.1f KB"), currentSize.ToDouble() / 1024);
                } else {
                    sizeStr = wxString::Format(wxT("%lld bytes"), currentSize.GetValue());
                }

                wxString msg = wxString::Format(wxT("正在压缩文件... (%s)"), sizeStr);
                if (!progressDialog->Update(progress, msg)) {
                    // User clicked cancel
                    cancelled = true;
                    break;
                }
            } else {
                stableCount++;
                // If size hasn't changed for a while, still show some progress
                if (stableCount > 5 && progress < 90) {
                    progress = wxMin(90, progress + 1);
                    wxString msg = wxString::Format(wxT("正在压缩文件... (正在处理)"));
                    if (!progressDialog->Update(progress, msg)) {
                        cancelled = true;
                        break;
                    }
                }
            }
        } else {
            // File not created yet, show initial progress
            progress = wxMin(15, progress + 1);
            if (!progressDialog->Update(progress, wxT("正在初始化压缩..."))) {
                cancelled = true;
                break;
            }
        }

        // Small delay to avoid busy waiting
        wxMilliSleep(300);
    }

    delete progressDialog;

    if (cancelled) {
        // Kill the tar process if user cancelled
        wxProcess::Kill(pid, wxSIGTERM);
        wxMessageBox(wxT("备份已取消"), wxT("取消"), wxOK | wxICON_INFORMATION, this);
        return false;
    }

    // Wait a bit more to ensure process completion
    wxMilliSleep(1000);

    // Check if backup file was created successfully
    if (!wxFileName::FileExists(backupPath)) {
        wxMessageBox(wxT("备份文件创建失败！"),
                     wxT("错误"), wxOK | wxICON_ERROR, this);
        return false;
    }

    // Verify backup file size
    wxULongLong finalSize = wxFileName::GetSize(backupPath);
    if (finalSize == 0) {
        wxMessageBox(wxT("备份文件无效（大小为0）！"),
                     wxT("错误"), wxOK | wxICON_ERROR, this);
        return false;
    }
    
    wxMessageBox(wxString::Format(wxT("备份完成！\n备份文件：%s"), backupPath), 
                 wxT("备份成功"), wxOK | wxICON_INFORMATION, this);
    return true;
}

bool DeleteSourceDialog::DeleteCppFiles()
{
    // Find OSS_MR path dynamically
    wxString ossMRPath = FindOSSMRPath();
    if (ossMRPath.IsEmpty()) {
        wxMessageBox(wxT("错误：未找到 OSS_MR 目录！"),
                     wxT("错误"), wxOK | wxICON_ERROR, this);
        return false;
    }

    // Directories to process (from the script)
    wxArrayString directories;
    directories.Add(ossMRPath + wxT("/src/mgsPkg"));
    directories.Add(ossMRPath + wxT("/src/http_module"));
    directories.Add(ossMRPath + wxT("/src/mcb_parser"));
    directories.Add(ossMRPath + wxT("/src/mcb_reader"));
    directories.Add(ossMRPath + wxT("/src/mcb_recorder"));
    directories.Add(ossMRPath + wxT("/src/process_collection"));
    
    wxString results;
    bool allSuccess = true;
    
    for (size_t i = 0; i < directories.GetCount(); i++) {
        const wxString& dir = directories[i];
        
        if (!wxDir::Exists(dir)) {
            results += wxString::Format(wxT("目录不存在: %s\n"), dir);
            continue;
        }
        
        // Recursively find and delete .cpp files
        int deletedCount = DeleteCppFilesRecursive(dir, results, allSuccess);

        results += wxString::Format(wxT("%s: 删除了 %d 个 .cpp 文件\n"),
                                   wxFileName(dir).GetName(), deletedCount);
    }
    
    if (allSuccess) {
        ShowResult(true, wxT("删除操作完成！\n\n") + results);
    } else {
        ShowResult(false, wxT("删除操作部分失败：\n\n") + results);
    }
    
    return allSuccess;
}

int DeleteSourceDialog::DeleteCppFilesRecursive(const wxString& dir, wxString& results, bool& allSuccess)
{
    int deletedCount = 0;

    wxDir directory(dir);
    if (!directory.IsOpened()) {
        return 0;
    }

    wxString filename;

    // First, delete .cpp files in current directory
    bool cont = directory.GetFirst(&filename, wxT("*.cpp"), wxDIR_FILES);
    while (cont) {
        wxString fullPath = dir + wxT("/") + filename;
        if (wxRemoveFile(fullPath)) {
            deletedCount++;
        } else {
            results += wxString::Format(wxT("删除失败: %s\n"), fullPath);
            allSuccess = false;
        }
        cont = directory.GetNext(&filename);
    }

    // Then, recursively process subdirectories
    cont = directory.GetFirst(&filename, wxEmptyString, wxDIR_DIRS);
    while (cont) {
        // Skip hidden directories and common build directories
        if (!filename.StartsWith(wxT(".")) &&
            filename != wxT("build") &&
            filename != wxT("cmake-build-debug") &&
            filename != wxT("cmake-build-release")) {

            wxString subDir = dir + wxT("/") + filename;
            deletedCount += DeleteCppFilesRecursive(subDir, results, allSuccess);
        }
        cont = directory.GetNext(&filename);
    }

    return deletedCount;
}

void DeleteSourceDialog::ShowResult(bool success, const wxString& message)
{
    wxMessageBox(message,
                 success ? wxT("操作完成") : wxT("操作失败"),
                 wxOK | (success ? wxICON_INFORMATION : wxICON_WARNING),
                 this);
}
