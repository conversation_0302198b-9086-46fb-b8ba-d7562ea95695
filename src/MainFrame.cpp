#include "MainFrame.h"
#include "DeleteSourceDialog.h"
#include "SystemInfoDialog.h"
#include "UdpTestDialog.h"
#include "ChangeConfigDialog.h"
#include "ReplaceAlgorithmDialog.h"
#include "AiChatDialog.h"
#include <wx/msgdlg.h>

wxBEGIN_EVENT_TABLE(MainFrame, wxFrame)
    EVT_MENU(wxID_EXIT, MainFrame::OnExit)
    EVT_MENU(wxID_ABOUT, MainFrame::OnAbout)
    EVT_BUTTON(ID_DELETE_SOURCE, MainFrame::OnDeleteSource)
    EVT_BUTTON(ID_SYSTEM_INFO, MainFrame::OnSystemInfo)
    EVT_BUTTON(ID_UDP_TEST, MainFrame::OnUdpTest)
    EVT_BUTTON(ID_CHANGE_CONFIG, MainFrame::OnChangeConfig)
    EVT_BUTTON(ID_REPLACE_ALGORITHM, MainFrame::OnReplaceAlgorithm)
    EVT_BUTTON(ID_AICHAT, MainFrame::OnAiChat)
    EVT_BUTTON(ID_LOGFILE_PACK, MainFrame::OnLogFilePack)
    EVT_BUTTON(ID_BUTTON_8, MainFrame::OnButtonClick)
    EVT_BUTTON(ID_BUTTON_9, MainFrame::OnButtonClick)
    EVT_BUTTON(ID_BUTTON_10, MainFrame::OnButtonClick)
    EVT_BUTTON(ID_BUTTON_11, MainFrame::OnButtonClick)
    EVT_BUTTON(ID_BUTTON_12, MainFrame::OnButtonClick)
    EVT_BUTTON(ID_BUTTON_13, MainFrame::OnButtonClick)
    EVT_BUTTON(ID_BUTTON_14, MainFrame::OnButtonClick)
    EVT_BUTTON(ID_BUTTON_15, MainFrame::OnButtonClick)
    EVT_BUTTON(ID_BUTTON_16, MainFrame::OnButtonClick)
wxEND_EVENT_TABLE()

MainFrame::MainFrame(const wxString& title, const wxPoint& pos, const wxSize& size)
    : wxFrame(NULL, wxID_ANY, title, pos, size), m_isLoggedIn(false)
{
    // Create menu bar
    CreateMenuBar();

    // Create main panel
    m_panel = new wxPanel(this, wxID_ANY);
    m_panel->SetBackgroundColour(wxColour(240, 240, 240));

    // Create button grid
    CreateButtonGrid();

    // Create status bar
    CreateStatusBar();

    // Initially hide all buttons until login
    SetLoggedIn(false);
    
    // Set up main sizer
    wxBoxSizer* mainSizer = new wxBoxSizer(wxVERTICAL);
    
    // Add title
    // wxStaticText* titleText = new wxStaticText(m_panel, wxID_ANY, 
    //     wxT("Mini Tool - 小工具集合"), 
    //     wxDefaultPosition, wxDefaultSize, wxALIGN_CENTRE_HORIZONTAL);
    // wxFont titleFont = titleText->GetFont();
    // titleFont.SetPointSize(16);
    // titleFont.SetWeight(wxFONTWEIGHT_BOLD);
    // titleText->SetFont(titleFont);
    
    // mainSizer->Add(titleText, 0, wxALL | wxCENTER, 20);
    mainSizer->Add(m_buttonSizer, 0, wxALL | wxEXPAND, 20);
    
    m_panel->SetSizer(mainSizer);
    
    // Center the frame
    Centre();
}

void MainFrame::CreateMenuBar()
{
    wxMenuBar* menuBar = new wxMenuBar;
    
    wxMenu* fileMenu = new wxMenu;
    fileMenu->Append(wxID_EXIT, wxT("退出\tCtrl+Q"), wxT("退出程序"));
    
    wxMenu* helpMenu = new wxMenu;
    helpMenu->Append(wxID_ABOUT, wxT("关于\tF1"), wxT("关于此程序"));
    
    menuBar->Append(fileMenu, wxT("文件"));
    menuBar->Append(helpMenu, wxT("帮助"));
    
    SetMenuBar(menuBar);
}

void MainFrame::CreateButtonGrid()
{
    // Create grid sizer for buttons (4 columns, expandable rows)
    m_buttonSizer = new wxGridSizer(INITIAL_ROWS, BUTTONS_PER_ROW, 10, 10);
    
    // Add functional buttons
    AddButton(wxT("修改探测器配置"), ID_CHANGE_CONFIG);
    AddButton(wxT("系统信息查询"), ID_SYSTEM_INFO);
    AddButton(wxT("阀组测试"), ID_UDP_TEST);
    AddButton(wxT("删除源码"), ID_DELETE_SOURCE);

    AddButton(wxT("替换算法"), ID_REPLACE_ALGORITHM);
    AddButton(wxT("智能聊天"), ID_AICHAT);
    AddButton(wxT("日志文件打包"), ID_LOGFILE_PACK);
    // Add placeholder buttons for future functionality
    for (int i = 8; i <= 16; i++) {
        wxString label = wxString::Format(wxT("功能 %d"), i);
        AddButton(label, ID_BUTTON_8 + i - 8, false);  // Disabled for now
    }
}

void MainFrame::CreateStatusBar()
{
    wxFrame::CreateStatusBar(1);
    SetStatusText(wxT("就绪"));
}

void MainFrame::AddButton(const wxString& label, int id, bool enabled)
{
    wxButton* button = new wxButton(m_panel, id, label, 
        wxDefaultPosition, wxSize(BUTTON_WIDTH, BUTTON_HEIGHT));
    
    if (!enabled) {
        button->Enable(false);
        button->SetBackgroundColour(wxColour(200, 200, 200));
    } else {
        button->SetBackgroundColour(wxColour(220, 240, 255));
    }
    
    // Set font
    wxFont buttonFont = button->GetFont();
    buttonFont.SetPointSize(12);
    button->SetFont(buttonFont);
    
    m_buttons.push_back(button);
    m_buttonSizer->Add(button, 0, wxEXPAND);
}

void MainFrame::SetButtonFunction(int buttonIndex, const wxString& label, int id)
{
    if (buttonIndex >= 0 && buttonIndex < (int)m_buttons.size()) {
        m_buttons[buttonIndex]->SetLabel(label);
        m_buttons[buttonIndex]->SetId(id);
        m_buttons[buttonIndex]->Enable(true);
        m_buttons[buttonIndex]->SetBackgroundColour(wxColour(220, 240, 255));
    }
}

void MainFrame::OnDeleteSource(wxCommandEvent& WXUNUSED(event))
{
    if (!m_isLoggedIn) {
        wxMessageBox(wxT("请先登录"), wxT("提示"), wxOK | wxICON_WARNING, this);
        return;
    }

    SetStatusText(wxT("准备删除源码..."));

    DeleteSourceDialog dialog(this);
    dialog.ShowModal();

    SetStatusText(wxT("就绪"));
    //     wxString message = wxString::Format(wxT("功能开发中，敬请期待"));
    // wxMessageBox(message, wxT("提示"), wxOK | wxICON_INFORMATION, this);
}

void MainFrame::OnSystemInfo(wxCommandEvent& WXUNUSED(event))
{
    if (!m_isLoggedIn) {
        wxMessageBox(wxT("请先登录"), wxT("提示"), wxOK | wxICON_WARNING, this);
        return;
    }

    SetStatusText(wxT("获取系统信息..."));

    SystemInfoDialog dialog(this);
    dialog.ShowModal();

    SetStatusText(wxT("就绪"));
}

void MainFrame::OnUdpTest(wxCommandEvent& WXUNUSED(event))
{
    if (!m_isLoggedIn) {
        wxMessageBox(wxT("请先登录"), wxT("提示"), wxOK | wxICON_WARNING, this);
        return;
    }

    SetStatusText(wxT("阀组测试..."));

    UdpTestDialog dialog(this);
    dialog.ShowModal();

    SetStatusText(wxT("就绪"));
}

void MainFrame::OnChangeConfig(wxCommandEvent& WXUNUSED(event))
{
    if (!m_isLoggedIn) {
        wxMessageBox(wxT("请先登录"), wxT("提示"), wxOK | wxICON_WARNING, this);
        return;
    }

    SetStatusText(wxT("修改配置..."));

    ChangeConfigDialog dialog(this);
    dialog.ShowModal();

    SetStatusText(wxT("就绪"));
}

void MainFrame::OnReplaceAlgorithm(wxCommandEvent& WXUNUSED(event))
{
    if (!m_isLoggedIn) {
        wxMessageBox(wxT("请先登录"), wxT("提示"), wxOK | wxICON_WARNING, this);
        return;
    }

    SetStatusText(wxT("替换算法..."));

    ReplaceAlgorithmDialog dialog(this);
    dialog.ShowModal();

    SetStatusText(wxT("就绪"));
}

void MainFrame::OnAiChat(wxCommandEvent& WXUNUSED(event))
{
    SetStatusText(wxT("智能聊天..."));

    AiChatDialog dialog(this);
    dialog.ShowModal();

    SetStatusText(wxT("就绪"));

    SetStatusText(wxT("就绪"));
}

void MainFrame::OnLogFilePack(wxCommandEvent& WXUNUSED(event))
{ 
}

void MainFrame::OnButtonClick(wxCommandEvent& event)
{
    int buttonId = event.GetId();
    wxString message = wxString::Format(wxT("功能尚未实现"));
    wxMessageBox(message, wxT("提示"), wxOK | wxICON_INFORMATION, this);
}

void MainFrame::OnExit(wxCommandEvent& WXUNUSED(event))
{
    Close(true);
}

void MainFrame::OnAbout(wxCommandEvent& WXUNUSED(event))
{
    wxMessageBox(wxT("Mini Tool v1.0.1\n\n一个集成各种小功能的工具程序\n\n开发者：Nuctech-Greg"),
                 wxT("关于 Mini Tool"), wxOK | wxICON_INFORMATION, this);
}

bool MainFrame::ShowLoginDialog()
{
    LoginDialog loginDlg(this);

    if (loginDlg.ShowModal() == wxID_OK) {
        m_currentUser = loginDlg.GetUsername();
        SetLoggedIn(true);

        // Update status bar with user info
        SetStatusText(wxString::Format(wxT("已登录用户: %s"), m_currentUser), 0);

        return true;
    }

    return false;
}

void MainFrame::SetLoggedIn(bool loggedIn)
{
    m_isLoggedIn = loggedIn;

    // Show/hide all functional buttons based on login status
    for (wxButton* button : m_buttons) {
        if (button) {
            button->Show(loggedIn);
        }
    }

    if (loggedIn) {
        SetTitle(wxString::Format(wxT("Mini Tool - 用户: %s"), m_currentUser));
    } else {
        SetTitle(wxT("Mini Tool - 未登录"));
        SetStatusText(wxT("请先登录"), 0);
    }

    // Refresh layout
    m_panel->Layout();
    Layout();
}
