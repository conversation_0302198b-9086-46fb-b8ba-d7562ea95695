#!/bin/bash
# -*- coding: utf-8 -*-
"""
RAG依赖安装脚本
为Qwen-Agent服务安装RAG功能所需的依赖包
"""

set -e  # 遇到错误立即退出

echo "=== 安装Qwen-Agent RAG功能依赖 ==="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python3"
    exit 1
fi

echo "Python版本: $(python3 --version)"

# 检查pip
if ! command -v pip3 &> /dev/null; then
    echo "错误: 未找到pip3，请先安装pip"
    exit 1
fi

# 升级pip
echo "升级pip..."
pip3 install --upgrade pip

# 检查虚拟环境
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "当前在虚拟环境中: $VIRTUAL_ENV"
else
    echo "警告: 建议在虚拟环境中安装依赖"
    read -p "是否继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "安装取消"
        exit 1
    fi
fi

# 安装基础依赖
echo "安装基础依赖..."
pip3 install -r requirements.txt

# 安装额外的RAG依赖（如果requirements.txt中没有包含）
echo "安装RAG相关依赖..."

# ChromaDB - 向量数据库
echo "安装ChromaDB..."
pip3 install chromadb>=0.4.0

# Sentence Transformers - 文本向量化
echo "安装Sentence Transformers..."
pip3 install sentence-transformers>=2.2.0

# PDF处理
echo "安装PDF处理库..."
pip3 install PyPDF2>=3.0.0

# Word文档处理
echo "安装Word文档处理库..."
pip3 install python-docx>=0.8.11

# 数值计算
echo "安装NumPy..."
pip3 install numpy>=1.21.0

# 文件上传安全处理
echo "安装Werkzeug..."
pip3 install werkzeug>=2.0.0

# 可选：安装更好的PDF处理库
echo "安装可选依赖..."
pip3 install pdfplumber>=0.7.0 || echo "pdfplumber安装失败，跳过"

# 验证安装
echo ""
echo "=== 验证安装 ==="

python3 -c "
import sys
packages = [
    'qwen_agent',
    'chromadb', 
    'sentence_transformers',
    'PyPDF2',
    'docx',
    'numpy',
    'flask',
    'flask_cors',
    'json5'
]

failed = []
for pkg in packages:
    try:
        __import__(pkg)
        print(f'✓ {pkg}')
    except ImportError as e:
        print(f'✗ {pkg}: {e}')
        failed.append(pkg)

if failed:
    print(f'\\n安装失败的包: {failed}')
    sys.exit(1)
else:
    print('\\n所有依赖安装成功！')
"

if [ $? -eq 0 ]; then
    echo ""
    echo "=== 安装完成 ==="
    echo "RAG功能依赖安装成功！"
    echo ""
    echo "下一步："
    echo "1. 启动服务: python3 qwen_agent_service.py"
    echo "2. 运行测试: python3 test_rag_functionality.py"
    echo ""
else
    echo ""
    echo "=== 安装失败 ==="
    echo "请检查错误信息并重新安装"
    exit 1
fi
