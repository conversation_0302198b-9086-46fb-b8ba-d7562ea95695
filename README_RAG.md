# Qwen-Agent RAG功能使用指南

本文档介绍如何使用增强版Qwen-Agent服务的RAG（检索增强生成）功能。

## 功能概述

RAG功能为Qwen-Agent添加了以下能力：
- 📄 **文档管理**: 支持上传、存储和管理各种格式的文档
- 🔍 **智能检索**: 基于语义相似性搜索相关文档内容
- 🤖 **增强问答**: 结合检索到的文档内容提供更准确的回答
- 📊 **向量化存储**: 使用ChromaDB进行高效的向量存储和检索

## 支持的文件格式

- **PDF文件** (.pdf) - 使用PyPDF2提取文本
- **Word文档** (.docx) - 使用python-docx处理
- **文本文件** (.txt) - 直接读取
- **HTML文件** (.html) - 提取文本内容
- **Markdown文件** (.md) - 支持Markdown格式

## 安装依赖

### 方法1: 使用安装脚本（推荐）

```bash
chmod +x install_rag_dependencies.sh
./install_rag_dependencies.sh
```

### 方法2: 手动安装

```bash
# 安装基础依赖
pip install -r requirements.txt

# 安装RAG相关依赖
pip install chromadb>=0.4.0
pip install sentence-transformers>=2.2.0
pip install PyPDF2>=3.0.0
pip install python-docx>=0.8.11
pip install numpy>=1.21.0
```

## 启动服务

```bash
python3 qwen_agent_service.py
```

服务启动后，RAG功能将自动初始化：
- 创建文档存储目录 `./rag_documents`
- 初始化向量数据库 `./rag_vector_db`
- 加载embedding模型 `all-MiniLM-L6-v2`

## API接口

### 1. 上传文档

**POST** `/rag/documents`

上传文档到知识库：

```bash
curl -X POST \
  -F "file=@document.pdf" \
  -F "uploaded_by=user1" \
  -F "description=技术文档" \
  http://localhost:5000/rag/documents
```

### 2. 获取文档列表

**GET** `/rag/documents`

```bash
curl http://localhost:5000/rag/documents
```

### 3. 删除文档

**DELETE** `/rag/documents/{doc_id}`

```bash
curl -X DELETE http://localhost:5000/rag/documents/abc123
```

### 4. 搜索知识库

**POST** `/rag/search`

```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"query": "设备维护", "top_k": 5}' \
  http://localhost:5000/rag/search
```

### 5. RAG聊天

**POST** `/chat`

普通聊天接口，Agent会自动使用RAG功能：

```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"message": "如何维护设备？"}' \
  http://localhost:5000/chat
```

## 使用示例

### Python客户端示例

```python
import requests

# 上传文档
with open('manual.pdf', 'rb') as f:
    files = {'file': ('manual.pdf', f, 'application/pdf')}
    response = requests.post('http://localhost:5000/rag/documents', files=files)
    print(response.json())

# 搜索知识库
search_data = {
    'query': '设备故障处理',
    'top_k': 3
}
response = requests.post('http://localhost:5000/rag/search', json=search_data)
print(response.json())

# RAG聊天
chat_data = {
    'message': '设备出现故障时应该怎么办？',
    'stream': False
}
response = requests.post('http://localhost:5000/chat', json=chat_data)
print(response.json())
```

### JavaScript客户端示例

```javascript
// 上传文档
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('description', '用户手册');

fetch('/rag/documents', {
    method: 'POST',
    body: formData
}).then(response => response.json())
  .then(data => console.log(data));

// RAG聊天
fetch('/chat', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        message: '请介绍一下产品特性',
        stream: false
    })
}).then(response => response.json())
  .then(data => console.log(data.response.content));
```

## 配置选项

在 `qwen_agent_service.py` 中可以修改RAG配置：

```python
RAG_CONFIG = {
    'documents_dir': './rag_documents',      # 文档存储目录
    'vector_db_dir': './rag_vector_db',      # 向量数据库目录
    'embedding_model': 'all-MiniLM-L6-v2',  # embedding模型
    'chunk_size': 1000,                      # 文本分块大小
    'chunk_overlap': 200,                    # 分块重叠大小
    'max_file_size': 50 * 1024 * 1024,      # 最大文件大小(50MB)
    'allowed_extensions': {'.pdf', '.txt', '.docx', '.html', '.md'}
}
```

## 测试功能

运行测试脚本验证RAG功能：

```bash
python3 test_rag_functionality.py
```

测试脚本会：
1. 检查服务状态
2. 创建测试文档
3. 上传文档到知识库
4. 测试文档搜索
5. 测试RAG聊天功能

## 工作原理

1. **文档处理**: 上传的文档被解析并提取文本内容
2. **文本分块**: 长文本被分割成较小的块以提高检索效果
3. **向量化**: 使用Sentence Transformers将文本转换为向量
4. **存储**: 向量和元数据存储在ChromaDB中
5. **检索**: 用户查询被向量化并与存储的向量进行相似性搜索
6. **生成**: 检索到的相关内容作为上下文传递给LLM生成回答

## 注意事项

- 首次启动时会下载embedding模型，需要网络连接
- 文档向量化需要一定时间，大文档处理较慢
- 向量数据库文件会占用磁盘空间
- 建议定期清理不需要的文档以节省空间

## 故障排除

### 常见问题

1. **依赖安装失败**
   - 确保Python版本 >= 3.8
   - 使用虚拟环境避免依赖冲突

2. **embedding模型下载失败**
   - 检查网络连接
   - 可以手动下载模型文件

3. **文档上传失败**
   - 检查文件格式是否支持
   - 确认文件大小不超过限制

4. **搜索结果为空**
   - 确认文档已成功上传和处理
   - 尝试不同的搜索关键词

## 性能优化

- 使用更强大的embedding模型提高检索质量
- 调整文本分块大小优化检索效果
- 定期清理向量数据库提高查询速度
- 考虑使用GPU加速embedding计算

## 扩展功能

RAG功能支持进一步扩展：
- 支持更多文件格式
- 集成更先进的检索算法
- 添加文档预处理功能
- 实现多语言支持
