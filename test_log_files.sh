#!/bin/bash

# 创建测试日志文件和目录结构
echo "创建测试日志文件和目录结构..."

# 创建OSS日志目录和文件
sudo mkdir -p /home/<USER>/oss_log/
sudo touch /home/<USER>/oss_log/nuctech_oss.log
sudo chmod 644 /home/<USER>/oss_log/nuctech_oss.log
echo "$(date): OSS测试日志内容" | sudo tee /home/<USER>/oss_log/nuctech_oss.log

# 创建MCB日志目录和文件
sudo mkdir -p /home/<USER>/Nuctech_Services/OSS_MR/config/
sudo touch /home/<USER>/Nuctech_Services/OSS_MR/config/HMCBCtrl.log
sudo chmod 644 /home/<USER>/Nuctech_Services/OSS_MR/config/HMCBCtrl.log
echo "$(date): MCB测试日志内容" | sudo tee /home/<USER>/Nuctech_Services/OSS_MR/config/HMCBCtrl.log

# 创建算法日志目录和文件
sudo mkdir -p /home/<USER>/Nuctech_Services/OSS_MR/Release/lib/mgspkg/AlgorithmData/
sudo touch /home/<USER>/Nuctech_Services/OSS_MR/Release/lib/mgspkg/AlgorithmData/ProcLog.log
sudo touch /home/<USER>/Nuctech_Services/OSS_MR/Release/lib/mgspkg/AlgorithmData/RunTime.log
sudo chmod 644 /home/<USER>/Nuctech_Services/OSS_MR/Release/lib/mgspkg/AlgorithmData/ProcLog.log
sudo chmod 644 /home/<USER>/Nuctech_Services/OSS_MR/Release/lib/mgspkg/AlgorithmData/RunTime.log
echo "$(date): 算法处理日志内容" | sudo tee /home/<USER>/Nuctech_Services/OSS_MR/Release/lib/mgspkg/AlgorithmData/ProcLog.log
echo "$(date): 算法运行时日志内容" | sudo tee /home/<USER>/Nuctech_Services/OSS_MR/Release/lib/mgspkg/AlgorithmData/RunTime.log

echo "测试日志文件创建完成！"
echo "文件列表："
echo "1. /home/<USER>/oss_log/nuctech_oss.log"
echo "2. /home/<USER>/Nuctech_Services/OSS_MR/config/HMCBCtrl.log"
echo "3. /home/<USER>/Nuctech_Services/OSS_MR/Release/lib/mgspkg/AlgorithmData/ProcLog.log"
echo "4. /home/<USER>/Nuctech_Services/OSS_MR/Release/lib/mgspkg/AlgorithmData/RunTime.log"
