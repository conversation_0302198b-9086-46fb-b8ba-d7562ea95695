Requirement already satisfied: sentence-transformers in ./venv/lib/python3.11/site-packages (5.0.0)
Requirement already satisfied: transformers<5.0.0,>=4.41.0 in ./venv/lib/python3.11/site-packages (from sentence-transformers) (4.54.1)
Requirement already satisfied: tqdm in ./venv/lib/python3.11/site-packages (from sentence-transformers) (4.67.1)
Requirement already satisfied: torch>=1.11.0 in ./venv/lib/python3.11/site-packages (from sentence-transformers) (2.7.1)
Requirement already satisfied: scikit-learn in ./venv/lib/python3.11/site-packages (from sentence-transformers) (1.7.1)
Requirement already satisfied: scipy in ./venv/lib/python3.11/site-packages (from sentence-transformers) (1.16.1)
Requirement already satisfied: huggingface-hub>=0.20.0 in ./venv/lib/python3.11/site-packages (from sentence-transformers) (0.34.3)
Requirement already satisfied: Pillow in ./venv/lib/python3.11/site-packages (from sentence-transformers) (11.3.0)
Requirement already satisfied: typing_extensions>=4.5.0 in ./venv/lib/python3.11/site-packages (from sentence-transformers) (4.14.0)
Requirement already satisfied: filelock in ./venv/lib/python3.11/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (3.18.0)
Requirement already satisfied: numpy>=1.17 in ./venv/lib/python3.11/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (2.3.1)
Requirement already satisfied: packaging>=20.0 in ./venv/lib/python3.11/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (25.0)
Requirement already satisfied: pyyaml>=5.1 in ./venv/lib/python3.11/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (6.0.2)
Requirement already satisfied: regex!=2019.12.17 in ./venv/lib/python3.11/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (2024.11.6)
Requirement already satisfied: requests in ./venv/lib/python3.11/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (2.32.4)
Requirement already satisfied: tokenizers<0.22,>=0.21 in ./venv/lib/python3.11/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (0.21.4)
Requirement already satisfied: safetensors>=0.4.3 in ./venv/lib/python3.11/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (0.5.3)
Requirement already satisfied: fsspec>=2023.5.0 in ./venv/lib/python3.11/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (2025.5.1)
Requirement already satisfied: hf-xet<2.0.0,>=1.1.3 in ./venv/lib/python3.11/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (1.1.5)
Requirement already satisfied: sympy>=1.13.3 in ./venv/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (1.14.0)
Requirement already satisfied: networkx in ./venv/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (3.5)
Requirement already satisfied: jinja2 in ./venv/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (3.1.6)
Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.6.77 in ./venv/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (12.6.77)
Requirement already satisfied: nvidia-cuda-runtime-cu12==12.6.77 in ./venv/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (12.6.77)
Requirement already satisfied: nvidia-cuda-cupti-cu12==12.6.80 in ./venv/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (12.6.80)
Requirement already satisfied: nvidia-cudnn-cu12==9.5.1.17 in ./venv/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (9.5.1.17)
Requirement already satisfied: nvidia-cublas-cu12==12.6.4.1 in ./venv/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (12.6.4.1)
Requirement already satisfied: nvidia-cufft-cu12==11.3.0.4 in ./venv/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (11.3.0.4)
Requirement already satisfied: nvidia-curand-cu12==10.3.7.77 in ./venv/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (10.3.7.77)
Requirement already satisfied: nvidia-cusolver-cu12==11.7.1.2 in ./venv/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (11.7.1.2)
Requirement already satisfied: nvidia-cusparse-cu12==12.5.4.2 in ./venv/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (12.5.4.2)
Requirement already satisfied: nvidia-cusparselt-cu12==0.6.3 in ./venv/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (0.6.3)
Requirement already satisfied: nvidia-nccl-cu12==2.26.2 in ./venv/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (2.26.2)
Requirement already satisfied: nvidia-nvtx-cu12==12.6.77 in ./venv/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (12.6.77)
Requirement already satisfied: nvidia-nvjitlink-cu12==12.6.85 in ./venv/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (12.6.85)
Requirement already satisfied: nvidia-cufile-cu12==1.11.1.6 in ./venv/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (1.11.1.6)
Requirement already satisfied: triton==3.3.1 in ./venv/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (3.3.1)
Requirement already satisfied: setuptools>=40.8.0 in ./venv/lib/python3.11/site-packages (from triton==3.3.1->torch>=1.11.0->sentence-transformers) (65.5.0)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in ./venv/lib/python3.11/site-packages (from sympy>=1.13.3->torch>=1.11.0->sentence-transformers) (1.3.0)
Requirement already satisfied: MarkupSafe>=2.0 in ./venv/lib/python3.11/site-packages (from jinja2->torch>=1.11.0->sentence-transformers) (3.0.2)
Requirement already satisfied: charset_normalizer<4,>=2 in ./venv/lib/python3.11/site-packages (from requests->transformers<5.0.0,>=4.41.0->sentence-transformers) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in ./venv/lib/python3.11/site-packages (from requests->transformers<5.0.0,>=4.41.0->sentence-transformers) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in ./venv/lib/python3.11/site-packages (from requests->transformers<5.0.0,>=4.41.0->sentence-transformers) (2.5.0)
Requirement already satisfied: certifi>=2017.4.17 in ./venv/lib/python3.11/site-packages (from requests->transformers<5.0.0,>=4.41.0->sentence-transformers) (2025.6.15)
Requirement already satisfied: joblib>=1.2.0 in ./venv/lib/python3.11/site-packages (from scikit-learn->sentence-transformers) (1.5.1)
Requirement already satisfied: threadpoolctl>=3.1.0 in ./venv/lib/python3.11/site-packages (from scikit-learn->sentence-transformers) (3.6.0)
