#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen-Agent 服务层 (增强版 - 支持RAG功能)
为 MiniTool C++ 应用提供 AI 聊天、工具调用和RAG检索服务
"""

import os
import json
import asyncio
import logging
import hashlib
import shutil
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from flask import Flask, request, jsonify, Response, stream_with_context, send_from_directory
from flask_cors import CORS
import threading
import time
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('qwen_agent_service.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 全局RAG管理器引用
_global_rag_manager = None

try:
    from qwen_agent.agents import Assistant
    from qwen_agent.tools.base import BaseTool, register_tool
    import json5

    # RAG相关导入
    import chromadb
    from chromadb.config import Settings
    import PyPDF2
    import docx
    from sentence_transformers import SentenceTransformer
    import numpy as np
    from werkzeug.utils import secure_filename

except ImportError as e:
    logger.error(f"导入依赖失败: {e}")
    logger.error("请安装所需依赖:")
    logger.error("pip install qwen-agent[gui,rag,code_interpreter,mcp]")
    logger.error("pip install chromadb sentence-transformers PyPDF2 python-docx")
    exit(1)

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# RAG配置
RAG_CONFIG = {
    'documents_dir': '/home/<USER>/database/rag_documents',
    'vector_db_dir': '/home/<USER>/database/rag_vector_db',
    'embedding_model': 'all-MiniLM-L6-v2',  # 轻量级embedding模型
    'chunk_size': 1000,
    'chunk_overlap': 200,
    'max_file_size': 50 * 1024 * 1024,  # 50MB
    'allowed_extensions': {'.pdf', '.txt', '.docx', '.html', '.md'}
}

# 知识搜索工具类
@register_tool('knowledge_search')
class KnowledgeSearchTool(BaseTool):
    description = '在知识库中搜索相关文档和信息，用于回答基于已有文档的问题'
    parameters = [{
        'name': 'query',
        'type': 'string',
        'description': '搜索查询，描述要查找的信息',
        'required': True
    }, {
        'name': 'top_k',
        'type': 'integer',
        'description': '返回结果数量，默认为5',
        'required': False
    }]

    def call(self, params: str, **kwargs) -> str:
        try:
            global _global_rag_manager
            if _global_rag_manager is None:
                return json5.dumps({'error': '知识库未初始化'}, ensure_ascii=False)

            params_dict = json5.loads(params)
            query = params_dict.get('query', '')
            top_k = params_dict.get('top_k', 5)

            if not query:
                return json5.dumps({'error': '查询不能为空'}, ensure_ascii=False)

            # 搜索文档
            results = _global_rag_manager.search_documents(query, top_k)

            if not results:
                return json5.dumps({
                    'message': '未找到相关文档',
                    'results': []
                }, ensure_ascii=False)

            # 格式化结果
            formatted_results = []
            for result in results:
                formatted_results.append({
                    'content': result['content'],
                    'filename': result['metadata'].get('filename', ''),
                    'similarity_score': round(result['similarity_score'], 3),
                    'rank': result['rank']
                })

            return json5.dumps({
                'message': f'找到 {len(results)} 个相关文档片段',
                'results': formatted_results
            }, ensure_ascii=False)

        except Exception as e:
            logger.error(f"知识搜索工具调用失败: {e}")
            return json5.dumps({'error': f'搜索失败: {str(e)}'}, ensure_ascii=False)

class RAGDocumentManager:
    """RAG文档管理器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.documents_dir = Path(config['documents_dir'])
        self.vector_db_dir = Path(config['vector_db_dir'])

        # 创建必要的目录
        self.documents_dir.mkdir(exist_ok=True)
        self.vector_db_dir.mkdir(exist_ok=True)

        # 初始化embedding模型
        try:
            self.embedding_model = SentenceTransformer(config['embedding_model'])
            logger.info(f"加载embedding模型: {config['embedding_model']}")
        except Exception as e:
            logger.warning(f"加载embedding模型失败: {e}, 将使用简单的文本匹配")
            self.embedding_model = None

        # 初始化向量数据库
        try:
            self.chroma_client = chromadb.PersistentClient(path=str(self.vector_db_dir))
            self.collection = self.chroma_client.get_or_create_collection(
                name="documents",
                metadata={"hnsw:space": "cosine"}
            )
            logger.info("初始化ChromaDB成功")
        except Exception as e:
            logger.warning(f"初始化ChromaDB失败: {e}, RAG功能将受限")
            self.chroma_client = None
            self.collection = None

    def extract_text_from_file(self, file_path: Path) -> str:
        """从文件中提取文本"""
        try:
            suffix = file_path.suffix.lower()

            if suffix == '.txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()

            elif suffix == '.pdf':
                text = ""
                with open(file_path, 'rb') as f:
                    reader = PyPDF2.PdfReader(f)
                    for page in reader.pages:
                        text += page.extract_text() + "\n"
                return text

            elif suffix == '.docx':
                doc = docx.Document(file_path)
                text = ""
                for paragraph in doc.paragraphs:
                    text += paragraph.text + "\n"
                return text

            elif suffix in ['.html', '.md']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()

            else:
                logger.warning(f"不支持的文件格式: {suffix}")
                return ""

        except Exception as e:
            logger.error(f"提取文件文本失败 {file_path}: {e}")
            return ""

    def chunk_text(self, text: str, chunk_size: int = None, overlap: int = None) -> List[str]:
        """将文本分块"""
        if not text.strip():
            return []

        chunk_size = chunk_size or self.config['chunk_size']
        overlap = overlap or self.config['chunk_overlap']

        chunks = []
        start = 0
        text_len = len(text)

        while start < text_len:
            end = start + chunk_size
            if end > text_len:
                end = text_len

            chunk = text[start:end]
            if chunk.strip():
                chunks.append(chunk.strip())

            if end == text_len:
                break

            start = end - overlap
            if start < 0:
                start = 0

        return chunks

    def add_document(self, file_path: Union[str, Path], metadata: Dict[str, Any] = None) -> bool:
        """添加文档到知识库"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                logger.error(f"文件不存在: {file_path}")
                return False

            # 检查文件大小
            if file_path.stat().st_size > self.config['max_file_size']:
                logger.error(f"文件过大: {file_path}")
                return False

            # 检查文件格式
            if file_path.suffix.lower() not in self.config['allowed_extensions']:
                logger.error(f"不支持的文件格式: {file_path.suffix}")
                return False

            # 提取文本
            text = self.extract_text_from_file(file_path)
            if not text.strip():
                logger.error(f"无法提取文本: {file_path}")
                return False

            # 分块
            chunks = self.chunk_text(text)
            if not chunks:
                logger.error(f"文本分块失败: {file_path}")
                return False

            # 生成文档ID
            doc_id = hashlib.md5(str(file_path).encode()).hexdigest()

            # 准备元数据
            doc_metadata = {
                'filename': file_path.name,
                'filepath': str(file_path),
                'doc_id': doc_id,
                'chunk_count': len(chunks),
                'created_at': datetime.now().isoformat()
            }
            if metadata:
                doc_metadata.update(metadata)

            # 存储到向量数据库
            if self.collection and self.embedding_model:
                try:
                    # 生成embeddings
                    embeddings = self.embedding_model.encode(chunks).tolist()

                    # 准备数据
                    ids = [f"{doc_id}_{i}" for i in range(len(chunks))]
                    metadatas = []
                    for i, chunk in enumerate(chunks):
                        chunk_metadata = doc_metadata.copy()
                        chunk_metadata.update({
                            'chunk_id': i,
                            'chunk_text': chunk[:200] + "..." if len(chunk) > 200 else chunk
                        })
                        metadatas.append(chunk_metadata)

                    # 添加到集合
                    self.collection.add(
                        embeddings=embeddings,
                        documents=chunks,
                        metadatas=metadatas,
                        ids=ids
                    )

                    logger.info(f"文档添加成功: {file_path.name}, 分块数: {len(chunks)}")
                    return True

                except Exception as e:
                    logger.error(f"向量化存储失败: {e}")
                    return False
            else:
                logger.warning("向量数据库未初始化，跳过向量化存储")
                return True

        except Exception as e:
            logger.error(f"添加文档失败: {e}")
            return False

    def search_documents(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """搜索相关文档"""
        try:
            if not self.collection or not self.embedding_model:
                logger.warning("向量数据库或embedding模型未初始化")
                return []

            # 生成查询向量
            query_embedding = self.embedding_model.encode([query]).tolist()

            # 搜索
            results = self.collection.query(
                query_embeddings=query_embedding,
                n_results=top_k,
                include=['documents', 'metadatas', 'distances']
            )

            # 格式化结果
            search_results = []
            if results['documents'] and results['documents'][0]:
                for i, (doc, metadata, distance) in enumerate(zip(
                    results['documents'][0],
                    results['metadatas'][0],
                    results['distances'][0]
                )):
                    search_results.append({
                        'content': doc,
                        'metadata': metadata,
                        'similarity_score': 1 - distance,  # 转换为相似度分数
                        'rank': i + 1
                    })

            return search_results

        except Exception as e:
            logger.error(f"文档搜索失败: {e}")
            return []

    def get_document_list(self) -> List[Dict[str, Any]]:
        """获取文档列表"""
        try:
            if not self.collection:
                return []

            # 获取所有文档的元数据
            results = self.collection.get(include=['metadatas'])

            # 按文档ID分组
            docs_dict = {}
            for metadata in results['metadatas']:
                doc_id = metadata.get('doc_id')
                if doc_id and doc_id not in docs_dict:
                    docs_dict[doc_id] = {
                        'doc_id': doc_id,
                        'filename': metadata.get('filename', ''),
                        'filepath': metadata.get('filepath', ''),
                        'chunk_count': metadata.get('chunk_count', 0),
                        'created_at': metadata.get('created_at', '')
                    }

            return list(docs_dict.values())

        except Exception as e:
            logger.error(f"获取文档列表失败: {e}")
            return []

    def delete_document(self, doc_id: str) -> bool:
        """删除文档"""
        try:
            if not self.collection:
                return False

            # 查找所有相关的chunk
            results = self.collection.get(
                where={"doc_id": doc_id},
                include=['ids']
            )

            if results['ids']:
                # 删除所有chunks
                self.collection.delete(ids=results['ids'])
                logger.info(f"删除文档成功: {doc_id}")
                return True
            else:
                logger.warning(f"未找到文档: {doc_id}")
                return False

        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            return False

class QwenAgentService:
    """Qwen-Agent 服务封装类 (增强版 - 支持RAG)"""

    def __init__(self):
        self.agent = None
        self.is_initialized = False
        self.chat_sessions = {}  # 存储多个聊天会话

        # 初始化RAG文档管理器
        self.rag_manager = RAGDocumentManager(RAG_CONFIG)

        # 注册RAG工具
        self.register_rag_tools()

        self.init_agent()

    def register_rag_tools(self):
        """注册RAG相关工具"""
        # 将 rag_manager 存储为全局变量，供工具使用
        global _global_rag_manager
        _global_rag_manager = self.rag_manager
    
    def init_agent(self):
        """初始化 Qwen-Agent"""
        try:
            # 显式设置环境变量禁用代理（在初始化前再次确保）
            import os
            os.environ.pop('http_proxy', None)
            os.environ.pop('https_proxy', None)
            os.environ.pop('HTTP_PROXY', None)
            os.environ.pop('HTTPS_PROXY', None)
            os.environ.pop('ALL_PROXY', None)
            os.environ.pop('all_proxy', None)
            # LLM 配置 - 使用本地 Ollama 服务
            llm_cfg = {
                'model': 'qwen3:32b',  # 与您现有配置保持一致
                'model_server': 'http://localhost:11434/v1',  # Ollama OpenAI 兼容接口
                'api_key': 'EMPTY',
                'generate_cfg': {
                    'top_p': 0.8,
                    'temperature': 0.7,
                    'max_tokens': 2000,
                }
            }
            
            # 工具配置 - 添加RAG工具
            tools = [
                {
                    'mcpServers': {
                        'filesystem': {
                            'command': './node_module/node_modules/.bin/mcp-server-filesystem',
                            'args':['/home/<USER>/Desktop', '/home/<USER>/','/home/<USER>/Nuctech_Services/']
                            # 'args':['-y','@modelcontextprotocol/server-filesystem' ,'/home/<USER>/Desktop', '/home/<USER>/']
                        }
                    }
                },
                'code_interpreter',  # 内置代码解释器
                'knowledge_search'   # RAG知识搜索工具
            ]
            
            # 系统提示 - 增强版包含RAG功能
            system_message = """你是 同方威视智能分选设备 的AI助手。你可以：
1. 回答用户的问题
2. 执行文件操作（读取、写入、列出文件等）
3. 运行和解释代码
4. 搜索知识库中的文档和信息（使用knowledge_search工具）
5. 基于已有文档提供准确的答案
6. 帮助用户完成各种任务

当用户询问特定信息时，请优先使用knowledge_search工具在知识库中搜索相关内容，然后基于搜索结果提供准确的回答。

请用中文回答，保持友好和专业的语调。"""
            
            # 创建 Agent
            self.agent = Assistant(
                llm=llm_cfg,
                function_list=tools,
                name='NUCTECH Intelligent Sorting AI Assistant',
                description='同方威视智能分选设备 的AI助手',
                system_message=system_message
            )
            
            self.is_initialized = True
            logger.info("Qwen-Agent 初始化成功")
            
        except Exception as e:
            logger.error(f"初始化 Qwen-Agent 失败: {e}")
            self.is_initialized = False
    
    def create_session(self, session_id: str = None) -> str:
        """创建新的聊天会话"""
        if not session_id:
            session_id = f"session_{int(time.time())}"
        
        self.chat_sessions[session_id] = {
            'messages': [],
            'created_at': datetime.now(),
            'last_activity': datetime.now()
        }
        
        logger.info(f"创建聊天会话: {session_id}")
        return session_id
    
    def get_session(self, session_id: str) -> Optional[Dict]:
        """获取聊天会话"""
        return self.chat_sessions.get(session_id)
    
    def update_session_activity(self, session_id: str):
        """更新会话活动时间"""
        if session_id in self.chat_sessions:
            self.chat_sessions[session_id]['last_activity'] = datetime.now()
    
    def chat(self, message: str, session_id: str = None, stream: bool = False):
        """处理聊天请求"""
        if not self.is_initialized:
            raise Exception("Agent 未初始化")
        
        # 获取或创建会话
        if not session_id:
            session_id = self.create_session()
        elif session_id not in self.chat_sessions:
            self.create_session(session_id)
        
        session = self.get_session(session_id)
        self.update_session_activity(session_id)
        
        # 添加用户消息
        user_message = {'role': 'user', 'content': message}
        session['messages'].append(user_message)
        
        try:
            # 调用 Agent
            messages_for_agent = session['messages'].copy()
            
            if stream:
                return self._stream_chat(messages_for_agent, session_id)
            else:
                return self._sync_chat(messages_for_agent, session_id)
                
        except Exception as e:
            logger.error(f"聊天处理失败: {e}")
            error_response = {
                'role': 'assistant',
                'content': f'抱歉，处理您的请求时出现错误：{str(e)}'
            }
            session['messages'].append(error_response)
            return error_response
    
    def _sync_chat(self, messages: List[Dict], session_id: str) -> Dict:
        """同步聊天处理"""
        response_messages = []
        
        for response in self.agent.run(messages=messages):
            response_messages.extend(response)
        
        # 更新会话
        session = self.get_session(session_id)
        session['messages'].extend(response_messages)
        
        # 返回最后一条助手消息
        assistant_messages = [msg for msg in response_messages if msg.get('role') == 'assistant']
        return assistant_messages[-1] if assistant_messages else {'role': 'assistant', 'content': '没有收到回复'}
    
    def _stream_chat(self, messages: List[Dict], session_id: str):
        """流式聊天处理"""
        def generate():
            try:
                response_messages = []
                current_content = ""
                
                for response in self.agent.run(messages=messages):
                    response_messages.extend(response)
                    
                    # 查找最新的助手消息
                    for msg in response:
                        if msg.get('role') == 'assistant':
                            content = msg.get('content', '')
                            if content and content != current_content:
                                # 发送增量内容
                                delta = content[len(current_content):]
                                current_content = content
                                
                                chunk_data = {
                                    'type': 'content',
                                    'delta': delta,
                                    'content': content
                                }
                                yield f"data: {json.dumps(chunk_data, ensure_ascii=False)}\n\n"
                
                # 更新会话
                session = self.get_session(session_id)
                session['messages'].extend(response_messages)
                
                # 发送完成信号
                yield f"data: {json.dumps({'type': 'done'}, ensure_ascii=False)}\n\n"
                
            except Exception as e:
                logger.error(f"流式聊天处理失败: {e}")
                error_data = {
                    'type': 'error',
                    'error': str(e)
                }
                yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
        
        return generate()
    
    def get_chat_history(self, session_id: str) -> List[Dict]:
        """获取聊天历史"""
        session = self.get_session(session_id)
        return session['messages'] if session else []
    
    def clear_chat_history(self, session_id: str) -> bool:
        """清空聊天历史"""
        if session_id in self.chat_sessions:
            self.chat_sessions[session_id]['messages'] = []
            return True
        return False

# 全局服务实例
qwen_service = QwenAgentService()

# API 路由定义
@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'ok',
        'initialized': qwen_service.is_initialized,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/chat', methods=['POST'])
def chat():
    """聊天接口"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据为空'}), 400
        
        message = data.get('message', '').strip()
        if not message:
            return jsonify({'error': '消息内容为空'}), 400
        
        session_id = data.get('session_id')
        stream = data.get('stream', False)
        
        if stream:
            # 流式响应
            def generate():
                yield "data: " + json.dumps({'type': 'start'}, ensure_ascii=False) + "\n\n"
                for chunk in qwen_service.chat(message, session_id, stream=True):
                    yield chunk
            
            return Response(
                stream_with_context(generate()),
                mimetype='text/event-stream',
                headers={
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'Access-Control-Allow-Origin': '*'
                }
            )
        else:
            # 同步响应
            response = qwen_service.chat(message, session_id, stream=False)
            return jsonify({
                'success': True,
                'response': response,
                'session_id': session_id
            })
    
    except Exception as e:
        logger.error(f"聊天接口错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/sessions', methods=['POST'])
def create_session():
    """创建新会话"""
    session_id = qwen_service.create_session()
    return jsonify({
        'success': True,
        'session_id': session_id
    })

@app.route('/sessions/<session_id>/history', methods=['GET'])
def get_history(session_id):
    """获取会话历史"""
    history = qwen_service.get_chat_history(session_id)
    return jsonify({
        'success': True,
        'history': history
    })

@app.route('/sessions/<session_id>/clear', methods=['POST'])
def clear_history(session_id):
    """清空会话历史"""
    success = qwen_service.clear_chat_history(session_id)
    return jsonify({
        'success': success
    })

# RAG相关API接口
@app.route('/rag/documents', methods=['POST'])
def upload_document():
    """上传文档到知识库"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有上传文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '文件名为空'}), 400

        # 检查文件扩展名
        filename = secure_filename(file.filename)
        file_ext = Path(filename).suffix.lower()

        if file_ext not in RAG_CONFIG['allowed_extensions']:
            return jsonify({
                'error': f'不支持的文件格式: {file_ext}',
                'allowed_extensions': list(RAG_CONFIG['allowed_extensions'])
            }), 400

        # 保存文件
        file_path = qwen_service.rag_manager.documents_dir / filename
        file.save(str(file_path))

        # 添加到知识库
        metadata = {
            'uploaded_by': request.form.get('uploaded_by', 'unknown'),
            'description': request.form.get('description', '')
        }

        success = qwen_service.rag_manager.add_document(file_path, metadata)

        if success:
            return jsonify({
                'success': True,
                'message': f'文档 {filename} 上传成功',
                'filename': filename
            })
        else:
            # 删除保存的文件
            if file_path.exists():
                file_path.unlink()
            return jsonify({'error': '文档处理失败'}), 500

    except Exception as e:
        logger.error(f"文档上传失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/rag/documents', methods=['GET'])
def get_documents():
    """获取知识库文档列表"""
    try:
        documents = qwen_service.rag_manager.get_document_list()
        return jsonify({
            'success': True,
            'documents': documents,
            'count': len(documents)
        })
    except Exception as e:
        logger.error(f"获取文档列表失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/rag/documents/<doc_id>', methods=['DELETE'])
def delete_document(doc_id):
    """删除知识库文档"""
    try:
        success = qwen_service.rag_manager.delete_document(doc_id)
        if success:
            return jsonify({
                'success': True,
                'message': f'文档 {doc_id} 删除成功'
            })
        else:
            return jsonify({'error': '文档删除失败'}), 404
    except Exception as e:
        logger.error(f"删除文档失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/rag/search', methods=['POST'])
def search_knowledge():
    """搜索知识库"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据为空'}), 400

        query = data.get('query', '').strip()
        if not query:
            return jsonify({'error': '搜索查询为空'}), 400

        top_k = data.get('top_k', 5)

        results = qwen_service.rag_manager.search_documents(query, top_k)

        return jsonify({
            'success': True,
            'query': query,
            'results': results,
            'count': len(results)
        })

    except Exception as e:
        logger.error(f"知识库搜索失败: {e}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    logger.info("启动 Qwen-Agent 服务...")
    
    # 检查依赖
    if not qwen_service.is_initialized:
        logger.error("服务初始化失败，退出")
        exit(1)
    
    # 启动服务
    app.run(
        host='127.0.0.1',
        port=5000,
        debug=False,
        threaded=True
    )
