#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAG功能测试脚本
测试Qwen-Agent服务的RAG功能，包括文档上传、搜索等
"""

import os
import json
import requests
import time
from pathlib import Path

# 服务配置
SERVICE_URL = "http://127.0.0.1:5000"
TEST_DOCUMENTS_DIR = Path("./test_documents")

def create_test_documents():
    """创建测试文档"""
    TEST_DOCUMENTS_DIR.mkdir(exist_ok=True)
    
    # 创建测试文本文档
    test_txt = TEST_DOCUMENTS_DIR / "test_knowledge.txt"
    with open(test_txt, 'w', encoding='utf-8') as f:
        f.write("""
同方威视智能分选设备技术文档

1. 设备概述
同方威视智能分选设备是一款基于AI技术的自动化分选系统，主要用于工业生产线上的产品质量检测和分类。

2. 技术特点
- 采用深度学习算法进行图像识别
- 支持多种产品类型的自动分选
- 具备高精度、高速度的处理能力
- 支持远程监控和数据分析

3. 主要功能
- 产品缺陷检测
- 自动分类分选
- 数据统计分析
- 远程监控管理

4. 技术参数
- 处理速度：1000件/分钟
- 识别精度：99.5%以上
- 支持产品尺寸：10-500mm
- 工作温度：-10°C到60°C

5. 应用场景
- 食品行业质量检测
- 电子元器件分选
- 医药产品检验
- 工业零件分类
        """)
    
    # 创建另一个测试文档
    test_txt2 = TEST_DOCUMENTS_DIR / "maintenance_guide.txt"
    with open(test_txt2, 'w', encoding='utf-8') as f:
        f.write("""
同方威视智能分选设备维护指南

1. 日常维护
- 每日清洁设备表面和传感器
- 检查传送带运行状态
- 确认各指示灯正常工作
- 记录设备运行数据

2. 周期性维护
- 每周校准传感器精度
- 检查机械部件磨损情况
- 更新软件版本
- 备份重要数据

3. 故障排除
- 设备无法启动：检查电源连接
- 识别精度下降：重新校准传感器
- 传送带异常：检查驱动系统
- 数据传输错误：检查网络连接

4. 安全注意事项
- 维护前必须断电
- 使用专用工具进行操作
- 遵循安全操作规程
- 定期进行安全培训
        """)
    
    return [test_txt, test_txt2]

def test_health_check():
    """测试服务健康状态"""
    print("=== 测试服务健康状态 ===")
    try:
        response = requests.get(f"{SERVICE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ 服务状态: {data['status']}")
            print(f"✓ 初始化状态: {data['initialized']}")
            return data['initialized']
        else:
            print(f"✗ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 连接服务失败: {e}")
        return False

def test_document_upload(file_path):
    """测试文档上传"""
    print(f"\n=== 测试文档上传: {file_path.name} ===")
    try:
        with open(file_path, 'rb') as f:
            files = {'file': (file_path.name, f, 'text/plain')}
            data = {
                'uploaded_by': 'test_user',
                'description': f'测试文档: {file_path.name}'
            }
            
            response = requests.post(f"{SERVICE_URL}/rag/documents", files=files, data=data)
            
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 上传成功: {result['message']}")
            return True
        else:
            print(f"✗ 上传失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"✗ 上传异常: {e}")
        return False

def test_document_list():
    """测试获取文档列表"""
    print("\n=== 测试获取文档列表 ===")
    try:
        response = requests.get(f"{SERVICE_URL}/rag/documents")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ 获取成功，共 {data['count']} 个文档:")
            for doc in data['documents']:
                print(f"  - {doc['filename']} (ID: {doc['doc_id'][:8]}...)")
            return data['documents']
        else:
            print(f"✗ 获取失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"✗ 获取异常: {e}")
        return []

def test_knowledge_search(query):
    """测试知识库搜索"""
    print(f"\n=== 测试知识库搜索: '{query}' ===")
    try:
        data = {
            'query': query,
            'top_k': 3
        }
        response = requests.post(f"{SERVICE_URL}/rag/search", json=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 搜索成功，找到 {result['count']} 个结果:")
            for i, item in enumerate(result['results'], 1):
                print(f"  {i}. 相似度: {item['similarity_score']:.3f}")
                print(f"     文件: {item['metadata']['filename']}")
                print(f"     内容: {item['content'][:100]}...")
                print()
            return result['results']
        else:
            print(f"✗ 搜索失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"✗ 搜索异常: {e}")
        return []

def test_chat_with_rag(message):
    """测试带RAG功能的聊天"""
    print(f"\n=== 测试RAG聊天: '{message}' ===")
    try:
        data = {
            'message': message,
            'stream': False
        }
        response = requests.post(f"{SERVICE_URL}/chat", json=data)
        
        if response.status_code == 200:
            result = response.json()
            print("✓ 聊天成功:")
            print(f"回答: {result['response']['content']}")
            return True
        else:
            print(f"✗ 聊天失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 聊天异常: {e}")
        return False

def main():
    """主测试函数"""
    print("开始RAG功能测试...")
    
    # 1. 检查服务状态
    if not test_health_check():
        print("服务未就绪，退出测试")
        return
    
    # 2. 创建测试文档
    print("\n创建测试文档...")
    test_files = create_test_documents()
    
    # 3. 上传测试文档
    upload_success = 0
    for file_path in test_files:
        if test_document_upload(file_path):
            upload_success += 1
    
    if upload_success == 0:
        print("没有文档上传成功，跳过后续测试")
        return
    
    # 等待文档处理完成
    print("\n等待文档处理...")
    time.sleep(3)
    
    # 4. 获取文档列表
    documents = test_document_list()
    
    # 5. 测试知识库搜索
    test_queries = [
        "设备的技术参数是什么",
        "如何进行日常维护",
        "处理速度",
        "故障排除方法"
    ]
    
    for query in test_queries:
        test_knowledge_search(query)
    
    # 6. 测试RAG聊天
    chat_questions = [
        "同方威视智能分选设备的主要技术特点是什么？",
        "设备出现故障时应该如何处理？",
        "设备的处理速度和识别精度如何？"
    ]
    
    for question in chat_questions:
        test_chat_with_rag(question)
        time.sleep(1)  # 避免请求过快
    
    print("\n=== RAG功能测试完成 ===")

if __name__ == "__main__":
    main()
