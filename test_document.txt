测试文档 - MiniTool 文档上传功能

这是一个用于测试MiniTool智能聊天界面文档上传功能的测试文档。

## 功能介绍

MiniTool现在支持以下文档上传功能：

1. **支持的文件格式**：
   - PDF文件 (.pdf)
   - Word文档 (.docx)
   - 文本文件 (.txt)
   - HTML文件 (.html)
   - Markdown文件 (.md)

2. **上传流程**：
   - 点击"📄 上传文档"按钮
   - 选择要上传的文档文件
   - 系统自动处理文档并添加到知识库
   - 上传成功后可以询问文档相关问题

3. **技术特性**：
   - 使用RAG（检索增强生成）技术
   - 基于ChromaDB向量数据库存储
   - 支持语义搜索和相似性匹配
   - 文档内容自动分块处理

## 使用说明

上传文档后，您可以询问如下问题：
- "这个文档讲了什么内容？"
- "文档中提到了哪些功能？"
- "如何使用文档上传功能？"

系统会基于上传的文档内容提供准确的回答。

## 技术实现

- 前端：wxWidgets C++界面
- 后端：Qwen-Agent Python服务
- 向量数据库：ChromaDB
- 文本嵌入：all-MiniLM-L6-v2模型
- 文档处理：支持多种格式解析

这个测试文档包含了关于MiniTool文档上传功能的详细信息，可以用来验证RAG功能是否正常工作。
